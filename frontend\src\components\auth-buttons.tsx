'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/hooks/use-auth'

interface AuthButtonsProps {
  onGetStarted?: () => void
}

// Declare global types for the window object
declare global {
  interface Window {
    __INITIAL_AUTH_STATE__?: boolean
    __THEME_READY__?: boolean
  }
}

export function AuthButtons({ onGetStarted }: AuthButtonsProps) {
  const router = useRouter()
  const { isLoggedIn, isHydrated } = useAuth()
  const [initialAuthState, setInitialAuthState] = useState<boolean | null>(null)

  useEffect(() => {
    // Get initial auth state from the script
    if (typeof window !== 'undefined') {
      setInitialAuthState(window.__INITIAL_AUTH_STATE__ || false)
    }
  }, [])

  const handleGetStarted = () => {
    if (onGetStarted) {
      onGetStarted()
    } else if (isLoggedIn || initialAuthState) {
      router.push('/dashboard')
    } else {
      router.push('/auth/register')
    }
  }

  // Use initial state if hydration hasn't completed yet
  const currentAuthState = isHydrated ? isLoggedIn : initialAuthState

  // Show loading only if we don't have initial state
  if (initialAuthState === null) {
    return (
      <div className="flex items-center space-x-4">
        <div className="w-20 h-9 bg-muted animate-pulse rounded-md" />
        <div className="w-24 h-9 bg-muted animate-pulse rounded-md" />
      </div>
    )
  }

  // Show appropriate buttons based on auth status
  if (currentAuthState) {
    return (
      <Button onClick={() => router.push('/dashboard')}>
        Dashboard
      </Button>
    )
  }

  return (
    <>
      <Button variant="ghost" onClick={() => router.push('/auth/login')}>
        Sign In
      </Button>
      <Button onClick={handleGetStarted}>
        Get Started
      </Button>
    </>
  )
}
