'use client'

import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { useAuth } from '@/hooks/use-auth'
import { Loader2 } from 'lucide-react'

interface AuthButtonsProps {
  onGetStarted?: () => void
}

export function AuthButtons({ onGetStarted }: AuthButtonsProps) {
  const router = useRouter()
  const { isLoggedIn, isHydrated } = useAuth()

  const handleGetStarted = () => {
    if (onGetStarted) {
      onGetStarted()
    } else if (isLoggedIn) {
      router.push('/dashboard')
    } else {
      router.push('/auth/register')
    }
  }

  // Show loading state while hydrating
  if (!isHydrated) {
    return (
      <div className="flex items-center space-x-4">
        <div className="w-20 h-9 bg-muted animate-pulse rounded-md" />
        <div className="w-24 h-9 bg-muted animate-pulse rounded-md" />
      </div>
    )
  }

  // Show appropriate buttons based on auth status
  if (isLoggedIn) {
    return (
      <Button onClick={() => router.push('/dashboard')}>
        Dashboard
      </Button>
    )
  }

  return (
    <>
      <Button variant="ghost" onClick={() => router.push('/auth/login')}>
        Sign In
      </Button>
      <Button onClick={handleGetStarted}>
        Get Started
      </Button>
    </>
  )
}
