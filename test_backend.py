#!/usr/bin/env python3
"""
Simple test script to verify backend functionality.
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_health():
    """Test health endpoint."""
    print("🔍 Testing health endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ Health check passed")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_register():
    """Test user registration."""
    print("\n🔍 Testing user registration...")
    user_data = {
        "full_name": "Test User",
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpassword123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/register", json=user_data)
        if response.status_code == 200:
            print("✅ User registration passed")
            return response.json()
        else:
            print(f"❌ User registration failed: {response.status_code}")
            print(response.text)
            return None
    except Exception as e:
        print(f"❌ User registration failed: {e}")
        return None

def test_login():
    """Test user login."""
    print("\n🔍 Testing user login...")
    login_data = {
        "username_or_email": "testuser",
        "password": "testpassword123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        if response.status_code == 200:
            print("✅ User login passed")
            return response.json()["access_token"]
        else:
            print(f"❌ User login failed: {response.status_code}")
            print(response.text)
            return None
    except Exception as e:
        print(f"❌ User login failed: {e}")
        return None

def test_create_project(token):
    """Test project creation."""
    print("\n🔍 Testing project creation...")
    project_data = {
        "name": "Test Project",
        "description": "A test project for the API"
    }
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.post(f"{BASE_URL}/projects/", json=project_data, headers=headers)
        if response.status_code == 200:
            print("✅ Project creation passed")
            return response.json()
        else:
            print(f"❌ Project creation failed: {response.status_code}")
            print(response.text)
            return None
    except Exception as e:
        print(f"❌ Project creation failed: {e}")
        return None

def test_get_projects(token):
    """Test getting user projects."""
    print("\n🔍 Testing get projects...")
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(f"{BASE_URL}/projects/", headers=headers)
        if response.status_code == 200:
            projects = response.json()
            print(f"✅ Get projects passed - Found {len(projects)} projects")
            return projects
        else:
            print(f"❌ Get projects failed: {response.status_code}")
            print(response.text)
            return None
    except Exception as e:
        print(f"❌ Get projects failed: {e}")
        return None

def main():
    """Run all tests."""
    print("🧪 Starting Codora Backend Tests...\n")
    
    # Test health
    if not test_health():
        print("\n❌ Backend is not running or not healthy")
        print("Please start the backend with: python run_backend.py")
        return False
    
    # Test registration
    user = test_register()
    if not user:
        print("\n❌ Registration test failed")
        return False
    
    # Test login
    token = test_login()
    if not token:
        print("\n❌ Login test failed")
        return False
    
    # Test project creation
    project = test_create_project(token)
    if not project:
        print("\n❌ Project creation test failed")
        return False
    
    # Test get projects
    projects = test_get_projects(token)
    if projects is None:
        print("\n❌ Get projects test failed")
        return False
    
    print("\n🎉 All tests passed!")
    print(f"✅ User created: {user['username']} ({user['email']})")
    print(f"✅ Project created: {project['name']} (ID: {project['id']})")
    print(f"✅ Found {len(projects)} projects")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💡 Make sure:")
        print("1. PostgreSQL is running")
        print("2. Database is set up (python setup_database.py)")
        print("3. Backend is running (python run_backend.py)")
        print("4. .env file is configured correctly")
    exit(0 if success else 1)
