"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/hooks/use-auth.ts":
/*!*******************************!*\
  !*** ./src/hooks/use-auth.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth auto */ \n\nfunction useAuth() {\n    const store = (0,_store_auth__WEBPACK_IMPORTED_MODULE_1__.useAuthStore)();\n    const [isHydrated, setIsHydrated] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Check if we're on the client side and store has hydrated\n        const checkHydration = ()=>{\n            if (true) {\n                // Check if localStorage has auth data\n                const authStorage = localStorage.getItem(\"auth-storage\");\n                if (authStorage) {\n                    // Small delay to ensure Zustand has hydrated\n                    setTimeout(()=>{\n                        setIsHydrated(true);\n                    }, 50);\n                } else {\n                    // No auth data, can hydrate immediately\n                    setIsHydrated(true);\n                }\n            }\n        };\n        checkHydration();\n    }, []);\n    return {\n        ...store,\n        isHydrated,\n        // Helper to check if user is truly authenticated (after hydration)\n        isReady: isHydrated && !store.isLoading,\n        isLoggedIn: isHydrated && store.isAuthenticated && !!store.token\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/use-auth.ts\n"));

/***/ })

});