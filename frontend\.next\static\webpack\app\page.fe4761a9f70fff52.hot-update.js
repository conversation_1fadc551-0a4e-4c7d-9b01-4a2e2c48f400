"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/store/auth.ts":
/*!***************************!*\
  !*** ./src/store/auth.ts ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: function() { return /* binding */ useAuthStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.persist)((set, get)=>({\n        // Initial state\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n        hasHydrated: false,\n        // Actions\n        login: async (credentials)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].login(credentials);\n                console.log(\"Auth store login response:\", response) // Debug log\n                ;\n                set({\n                    user: response.user,\n                    token: response.access_token,\n                    isAuthenticated: true,\n                    isLoading: false,\n                    error: null\n                });\n            } catch (error) {\n                var _error_response_data, _error_response;\n                console.error(\"Auth store login error:\", error) // Debug log\n                ;\n                set({\n                    error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || \"Login failed\",\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        register: async (userData)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].register(userData);\n                console.log(\"Auth store register response:\", response) // Debug log\n                ;\n                set({\n                    user: response.user,\n                    token: response.access_token,\n                    isAuthenticated: true,\n                    isLoading: false,\n                    error: null\n                });\n            } catch (error) {\n                var _error_response_data, _error_response;\n                console.error(\"Auth store register error:\", error) // Debug log\n                ;\n                set({\n                    error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || \"Registration failed\",\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        logout: ()=>{\n            _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].logout();\n            set({\n                user: null,\n                token: null,\n                isAuthenticated: false,\n                error: null\n            });\n        },\n        getCurrentUser: async ()=>{\n            const { token } = get();\n            if (!token) {\n                console.log(\"No token in getCurrentUser\") // Debug log\n                ;\n                return;\n            }\n            set({\n                isLoading: true\n            });\n            try {\n                const user = await _lib_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getCurrentUser();\n                console.log(\"getCurrentUser success:\", user) // Debug log\n                ;\n                set({\n                    user,\n                    isAuthenticated: true,\n                    isLoading: false,\n                    error: null\n                });\n            } catch (error) {\n                var _error_response;\n                console.error(\"getCurrentUser error:\", error) // Debug log\n                ;\n                // If token is invalid, logout\n                if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                    console.log(\"Token invalid, logging out\") // Debug log\n                    ;\n                    get().logout();\n                } else {\n                    var _error_response_data, _error_response1;\n                    set({\n                        error: ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || \"Failed to get user info\",\n                        isLoading: false\n                    });\n                }\n            }\n        },\n        clearError: ()=>set({\n                error: null\n            }),\n        setLoading: (loading)=>set({\n                isLoading: loading\n            })\n    }), {\n    name: \"auth-storage\",\n    partialize: (state)=>({\n            user: state.user,\n            token: state.token,\n            isAuthenticated: state.isAuthenticated\n        }),\n    onRehydrateStorage: ()=>(state)=>{\n            console.log(\"Auth store rehydrated:\", state) // Debug log\n            ;\n            if (state === null || state === void 0 ? void 0 : state.token) {\n                console.log(\"Token found in storage:\", !!state.token) // Debug log\n                ;\n                // Set hasHydrated to true after rehydration\n                state.hasHydrated = true;\n            }\n        }\n}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/auth.ts\n"));

/***/ })

});