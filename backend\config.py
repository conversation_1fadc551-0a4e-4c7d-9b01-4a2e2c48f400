"""
Configuration settings for the Codora application.
"""

from pydantic_settings import BaseSettings
from typing import List


class Settings(BaseSettings):
    """Application settings."""
    
    # Database
    database_url: str = "postgresql://username:password@localhost:5432/codora"
    
    # JWT
    secret_key: str = "your-super-secret-key-here-change-this-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # AI Model
    openai_api_key: str = ""
    model_name: str = "gpt-4o-mini"
    model_provider: str = "openai"
    
    # Application
    debug: bool = True
    cors_origins: List[str] = ["http://localhost:3000"]
    
    class Config:
        env_file = ".env"


settings = Settings()
