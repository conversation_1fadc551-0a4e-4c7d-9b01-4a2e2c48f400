"""
Configuration settings for the Codora application.
"""

from typing import List


class Settings:
    """Application settings."""
    
    # Database
    database_url: str = "postgresql://postgres:abood.2006@localhost:5432/codora"
    
    # JWT
    secret_key: str = "your-super-secret-key-here-change-this-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # AI Model
    openai_api_key: str = "AIzaSyBM0_Z7RG_bgvotteNW2I4ZX-3RkmWRduU"
    openai_base_url: str = "http://localhost:4141/"
    model_name: str = "gemini-2.5-pro-preview-05-06"
    model_provider: str = "openai"
    
    # Application
    debug: bool = True
    cors_origins: List[str] = ["http://localhost:3000"]


settings = Settings()
