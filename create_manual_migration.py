#!/usr/bin/env python3
"""
Manual migration script to create all tables.
Use this if Alembic autogenerate fails.
"""

from backend.database import engine, Base
from backend.models import User, Project, ProjectVersion, Message, File, ToolExecution

def create_all_tables():
    """Create all tables manually."""
    print("🔄 Creating all database tables...")
    try:
        Base.metadata.create_all(bind=engine)
        print("✅ All tables created successfully!")
        return True
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        return False

if __name__ == "__main__":
    success = create_all_tables()
    if success:
        print("\n🎉 Database setup completed!")
        print("You can now start the backend server.")
    else:
        print("\n💡 Please check your database connection and try again.")
    exit(0 if success else 1)
