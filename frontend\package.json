{"name": "codora-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "typescript": "^5.3.3", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "lucide-react": "^0.303.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-button": "^0.1.0", "@radix-ui/react-card": "^0.1.0", "@radix-ui/react-input": "^0.1.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-textarea": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "axios": "^1.6.2", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "date-fns": "^3.0.6", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "@types/react-syntax-highlighter": "^15.5.11", "monaco-editor": "^0.45.0", "@monaco-editor/react": "^4.6.0", "react-hotkeys-hook": "^4.4.1", "use-debounce": "^10.0.0", "react-query": "^3.39.3", "zustand": "^4.4.7"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-next": "14.0.4", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9"}}