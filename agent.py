from typing import Dict, Any, List
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage, ToolMessage
from langchain.chat_models import init_chat_model
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver
from prompt import SYSTEM_PROMPT
from state import AgentState
import tools

class Codora:
    """AI Web Development Assistant using LangGraph."""

    def __init__(self, model_name: str = "gpt-4o-mini", model_provider: str = "openai"):
        """Initialize the agent with specified model."""
        self.llm = init_chat_model(model_name, model_provider=model_provider)
        self.tools = [
            tools.create_file,
            tools.create_folder,
            tools.read_file,
            tools.list_directory,
            tools.replace_in_file,
            tools.search_in_files,
            tools.move_file_or_folder,
            tools.delete_file_or_folder,
            tools.update_file
        ]
        self.llm_with_tools = self.llm.bind_tools(self.tools)
        self.graph = self._create_graph()

    def _create_graph(self) -> StateGraph:
        """Create the LangGraph workflow."""
        workflow = StateGraph(AgentState)
        workflow.add_node("agent", self._agent_node)
        workflow.add_node("tools", self._tools_node)
        workflow.add_edge(START, "agent")
        workflow.add_conditional_edges(
            "agent",
            self._should_continue,
            {
                "continue": "tools",
                "end": END,
            }
        )
        workflow.add_edge("tools", "agent")
        memory = MemorySaver()
        graph = workflow.compile(checkpointer=memory)
        return graph

    def _tools_node(self, state: AgentState) -> Dict[str, Any]:
        """Custom tools node for Gemini compatibility."""
        result = []
        messages = state["messages"]
        last_message = state["messages"][-1]
        tool_calls = last_message.tool_calls if hasattr(last_message, 'tool_calls') else []
        tools_by_name = {tool.name: tool for tool in self.tools}
        for tool_call in tool_calls:
            tool = tools_by_name[tool_call["name"]]
            observation = tool.invoke(tool_call["args"])
            result.append(ToolMessage(content=observation, tool_call_id=tool_call["id"], name=tool.name))
        return {"messages": messages + result}

    def _agent_node(self, state: AgentState) -> Dict[str, Any]:
        """Main agent reasoning node."""
        system_prompt = SYSTEM_PROMPT
        messages = [
            SystemMessage(content=system_prompt.format(
                current_directory=state.get("current_directory", "."),
                recent_files=", ".join(state.get("recent_files", []))
            ))
        ] + state["messages"]
        for message in messages:
            if not message.content:
                message.content = "Response:"

        try:
            response = self.llm_with_tools.invoke(messages)
        except Exception as e:
            print(e)
            response = AIMessage(content="I apologize, but I encountered an error. Let me try to help you without using tools for now.")

        return {
            "messages": state["messages"] + [response],
            "waiting_for_user": False,
            "retry_count": 0
        }

    def _should_continue(self, state: AgentState) -> str:
        """Determine whether to continue with tools or end."""
        last_message = state["messages"][-1]
        if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
            return "continue"
        return "end"

    def process_message(self, user_input: str, thread_id: str = "default") -> str:
        """Process a user message and return the agent's response."""
        try:
            config = {"configurable": {"thread_id": thread_id}}
            current_state = self.graph.get_state(config)

            if not current_state.values:
                initial_state = {
                    "messages": [],
                    "current_task": None,
                    "project_name": None,
                    "project_description": None,
                    "current_directory": ".",
                    "recent_files": [],
                    "thoughts": [],
                    "tool_results": [],
                    "waiting_for_user": False,
                    "last_error": None,
                    "retry_count": 0
                }
            else:
                initial_state = current_state.values
            
            initial_state["messages"].append(HumanMessage(content=user_input))

            for state in self.graph.stream(initial_state, config, stream_mode="values"):
                yield state["messages"][-1]
                
        except Exception as e:
            yield f"Error: {str(e)}"
