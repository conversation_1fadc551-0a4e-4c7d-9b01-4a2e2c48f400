# Codora Frontend

Modern React frontend for Codora AI Web Development Assistant built with Next.js, TypeScript, Tailwind CSS, and Shadcn/ui.

## Features

- 🎨 **Modern UI** - Beautiful interface with Shadcn/ui components
- 🔐 **Authentication** - Secure login and registration
- 💬 **Real-time Chat** - Live conversation with AI agent
- 📁 **Project Management** - Create, edit, and organize projects
- 🔄 **Version Control** - Visual version tree and time travel
- 📄 **File Explorer** - Browse and edit project files
- 🌐 **Live Preview** - Instant website preview
- 📱 **Responsive Design** - Works on all devices
- 🌙 **Dark Mode** - Light and dark theme support
- ⚡ **Performance** - Optimized for speed and efficiency

## Tech Stack

- **Next.js 14** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **Shadcn/ui** - Beautiful and accessible components
- **Radix UI** - Unstyled, accessible UI primitives
- **Lucide React** - Beautiful icons
- **Axios** - HTTP client for API calls
- **React Hook Form** - Form handling
- **Zod** - Schema validation
- **Zustand** - State management
- **Monaco Editor** - Code editor
- **React Query** - Data fetching and caching

## Quick Start

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Backend server running on http://localhost:8000

### Installation

1. **Install dependencies**:
```bash
cd frontend
npm install
```

2. **Set up environment variables**:
```bash
cp .env.local.example .env.local
```

Edit `.env.local`:
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
NEXT_PUBLIC_APP_NAME=Codora
```

3. **Start development server**:
```bash
npm run dev
```

The app will be available at http://localhost:3000

## Project Structure

```
frontend/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── auth/              # Authentication pages
│   │   ├── dashboard/         # Main dashboard
│   │   ├── project/           # Project pages
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Home page
│   ├── components/            # React components
│   │   ├── ui/               # Shadcn/ui components
│   │   ├── auth/             # Auth components
│   │   ├── dashboard/        # Dashboard components
│   │   ├── chat/             # Chat components
│   │   ├── project/          # Project components
│   │   ├── file/             # File components
│   │   └── version/          # Version components
│   ├── hooks/                # Custom React hooks
│   ├── lib/                  # Utility libraries
│   │   ├── api.ts            # API client
│   │   └── utils.ts          # Helper functions
│   ├── store/                # State management
│   ├── types/                # TypeScript types
│   └── utils/                # Utility functions
├── public/                   # Static assets
├── package.json             # Dependencies
├── tailwind.config.js       # Tailwind configuration
├── tsconfig.json           # TypeScript configuration
└── next.config.js          # Next.js configuration
```

## Available Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript check

# Utilities
npm run clean        # Clean build files
npm run analyze      # Analyze bundle size
```

## Key Features

### Authentication System
- Secure JWT-based authentication
- Login with username or email
- User registration with validation
- Automatic token refresh
- Protected routes

### Real-time Chat Interface
- Live conversation with AI agent
- Streaming responses
- Message history
- Tool execution tracking
- Typing indicators

### Project Management
- Create and organize projects
- Project sharing with tokens
- Fork existing projects
- Project settings and metadata
- Collaborative features

### File System
- Visual file explorer
- Code editor with syntax highlighting
- File creation and editing
- File type detection
- Search and navigation

### Version Control
- Visual version tree
- Time travel between versions
- Version comparison
- Branching visualization
- Change tracking

### Live Preview
- Instant website preview
- Toggle between code and preview
- Responsive preview modes
- Full-screen preview
- Preview sharing

## Component Library

### UI Components (Shadcn/ui)
- Button, Input, Card, Dialog
- Dropdown, Tooltip, Tabs
- Avatar, Badge, Progress
- Toast notifications
- Form components

### Custom Components
- ChatInterface
- FileExplorer
- VersionTree
- CodeEditor
- ProjectCard
- UserMenu

## State Management

Using Zustand for lightweight state management:

```typescript
// Auth store
const useAuthStore = create((set) => ({
  user: null,
  token: null,
  login: (user, token) => set({ user, token }),
  logout: () => set({ user: null, token: null }),
}))

// Project store
const useProjectStore = create((set) => ({
  projects: [],
  currentProject: null,
  setProjects: (projects) => set({ projects }),
  setCurrentProject: (project) => set({ currentProject: project }),
}))
```

## API Integration

Centralized API client with automatic token handling:

```typescript
// API calls
const projects = await apiClient.getProjects()
const response = await apiClient.sendMessage(projectId, message)

// Streaming
const stream = await apiClient.sendMessage(projectId, message)
const reader = stream.body.getReader()
// Handle streaming response
```

## Styling Guidelines

### Tailwind CSS Classes
- Use semantic color tokens: `bg-background`, `text-foreground`
- Responsive design: `md:flex-row`, `lg:grid-cols-3`
- Dark mode support: `dark:bg-gray-800`
- Component variants: `hover:bg-accent`

### Custom CSS
- Minimal custom CSS in globals.css
- Use CSS variables for theming
- Animations with Tailwind utilities
- Custom scrollbars and transitions

## Performance Optimization

- **Code Splitting**: Automatic with Next.js
- **Image Optimization**: Next.js Image component
- **Bundle Analysis**: Built-in analyzer
- **Caching**: React Query for API caching
- **Lazy Loading**: Dynamic imports for heavy components

## Accessibility

- **Keyboard Navigation**: Full keyboard support
- **Screen Readers**: ARIA labels and roles
- **Color Contrast**: WCAG compliant colors
- **Focus Management**: Proper focus handling
- **Semantic HTML**: Meaningful markup

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Development Guidelines

### Code Style
- Use TypeScript for type safety
- Follow React best practices
- Use functional components with hooks
- Implement proper error boundaries
- Write meaningful component names

### Testing
```bash
npm run test        # Run unit tests
npm run test:e2e    # Run end-to-end tests
npm run coverage    # Generate coverage report
```

### Deployment
```bash
npm run build       # Build for production
npm run export      # Export static files
npm run deploy      # Deploy to hosting
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NEXT_PUBLIC_API_URL` | Backend API URL | `http://localhost:8000` |
| `NEXT_PUBLIC_WS_URL` | WebSocket URL | `ws://localhost:8000` |
| `NEXT_PUBLIC_APP_NAME` | Application name | `Codora` |
| `NODE_ENV` | Environment | `development` |

## Troubleshooting

### Common Issues

1. **API Connection Error**
   - Check backend is running on port 8000
   - Verify NEXT_PUBLIC_API_URL in .env.local
   - Check CORS settings

2. **Build Errors**
   - Run `npm run type-check` for TypeScript errors
   - Clear `.next` folder and rebuild
   - Check for missing dependencies

3. **Styling Issues**
   - Verify Tailwind CSS is properly configured
   - Check for conflicting CSS classes
   - Ensure dark mode variables are set

## Contributing

1. Fork the repository
2. Create a feature branch
3. Follow the coding guidelines
4. Add tests for new features
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
