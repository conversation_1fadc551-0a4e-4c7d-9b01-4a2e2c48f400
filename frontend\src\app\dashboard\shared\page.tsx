'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Search,
  MoreVertical,
  Eye,
  GitFork,
  Star,
  Download,
  Share2,
  Globe,
  Calendar,
  User,
  Heart,
  TrendingUp,
  Filter,
} from 'lucide-react'

interface SharedProject {
  id: number
  name: string
  description: string
  author: {
    name: string
    username: string
    avatar?: string
  }
  created_at: string
  updated_at: string
  stars: number
  forks: number
  views: number
  tags: string[]
  is_starred: boolean
  is_forked: boolean
  preview_url?: string
}

export default function SharedProjectsPage() {
  const [projects, setProjects] = useState<SharedProject[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [filter, setFilter] = useState<'all' | 'popular' | 'recent' | 'starred'>('all')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Mock shared projects data
    const mockProjects: SharedProject[] = [
      {
        id: 101,
        name: 'Modern E-commerce Template',
        description: 'A fully responsive e-commerce website template with shopping cart, product catalog, and checkout functionality.',
        author: {
          name: 'Sarah Johnson',
          username: 'sarahj',
        },
        created_at: '2024-01-15T10:30:00Z',
        updated_at: '2024-01-20T14:45:00Z',
        stars: 245,
        forks: 67,
        views: 1250,
        tags: ['e-commerce', 'responsive', 'tailwind'],
        is_starred: false,
        is_forked: false,
        preview_url: 'https://example.com/preview1'
      },
      {
        id: 102,
        name: 'Creative Portfolio Showcase',
        description: 'A stunning portfolio website for creative professionals with smooth animations and modern design.',
        author: {
          name: 'Alex Chen',
          username: 'alexc',
        },
        created_at: '2024-01-12T09:15:00Z',
        updated_at: '2024-01-18T16:20:00Z',
        stars: 189,
        forks: 43,
        views: 890,
        tags: ['portfolio', 'creative', 'animations'],
        is_starred: true,
        is_forked: false,
        preview_url: 'https://example.com/preview2'
      },
      {
        id: 103,
        name: 'SaaS Landing Page',
        description: 'Professional SaaS landing page with pricing tables, testimonials, and conversion-optimized design.',
        author: {
          name: 'Mike Rodriguez',
          username: 'miker',
        },
        created_at: '2024-01-08T14:00:00Z',
        updated_at: '2024-01-15T11:30:00Z',
        stars: 156,
        forks: 89,
        views: 2100,
        tags: ['saas', 'landing-page', 'conversion'],
        is_starred: false,
        is_forked: true,
        preview_url: 'https://example.com/preview3'
      },
      {
        id: 104,
        name: 'Restaurant Website',
        description: 'Beautiful restaurant website with menu display, online reservations, and location information.',
        author: {
          name: 'Emma Wilson',
          username: 'emmaw',
        },
        created_at: '2024-01-05T11:45:00Z',
        updated_at: '2024-01-12T13:20:00Z',
        stars: 98,
        forks: 34,
        views: 567,
        tags: ['restaurant', 'menu', 'reservations'],
        is_starred: false,
        is_forked: false,
        preview_url: 'https://example.com/preview4'
      },
      {
        id: 105,
        name: 'Blog Template',
        description: 'Clean and minimal blog template with article management, categories, and responsive design.',
        author: {
          name: 'David Kim',
          username: 'davidk',
        },
        created_at: '2024-01-01T08:30:00Z',
        updated_at: '2024-01-08T10:15:00Z',
        stars: 134,
        forks: 56,
        views: 780,
        tags: ['blog', 'minimal', 'cms'],
        is_starred: true,
        is_forked: false,
        preview_url: 'https://example.com/preview5'
      }
    ]

    setTimeout(() => {
      setProjects(mockProjects)
      setIsLoading(false)
    }, 1000)
  }, [])

  const filteredProjects = projects.filter(project => {
    const matchesSearch = 
      project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase())) ||
      project.author.name.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesFilter = 
      filter === 'all' ||
      (filter === 'popular' && project.stars > 150) ||
      (filter === 'recent' && new Date(project.updated_at) > new Date('2024-01-15')) ||
      (filter === 'starred' && project.is_starred)

    return matchesSearch && matchesFilter
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const handleToggleStar = (projectId: number) => {
    setProjects(prev => prev.map(project => 
      project.id === projectId 
        ? { 
            ...project, 
            is_starred: !project.is_starred,
            stars: project.is_starred ? project.stars - 1 : project.stars + 1
          }
        : project
    ))
  }

  const handleForkProject = (projectId: number) => {
    setProjects(prev => prev.map(project => 
      project.id === projectId 
        ? { 
            ...project, 
            is_forked: true,
            forks: project.forks + 1
          }
        : project
    ))
    // TODO: Implement actual fork functionality
    console.log('Fork project:', projectId)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Shared Projects</h1>
          <p className="text-muted-foreground mt-2">
            Discover and fork amazing projects from the community
          </p>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search projects, tags, or authors..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          {(['all', 'popular', 'recent', 'starred'] as const).map((filterOption) => (
            <Button
              key={filterOption}
              variant={filter === filterOption ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter(filterOption)}
              className="capitalize"
            >
              {filterOption === 'popular' && <TrendingUp className="h-3 w-3 mr-1" />}
              {filterOption === 'starred' && <Star className="h-3 w-3 mr-1" />}
              {filterOption}
            </Button>
          ))}
        </div>
      </div>

      {/* Projects Grid */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-muted rounded w-3/4" />
                <div className="h-3 bg-muted rounded w-full" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-muted rounded w-1/2" />
                  <div className="h-3 bg-muted rounded w-1/3" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredProjects.length === 0 ? (
        <div className="text-center py-12">
          <Share2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">
            No projects found
          </h3>
          <p className="text-muted-foreground">
            Try adjusting your search or filters
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects.map((project) => (
            <Card key={project.id} className="group hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-lg truncate">
                      <Link 
                        href={`/dashboard/shared/${project.id}`}
                        className="hover:text-primary transition-colors"
                      >
                        {project.name}
                      </Link>
                    </CardTitle>
                    <CardDescription className="line-clamp-2 mt-1">
                      {project.description}
                    </CardDescription>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem asChild>
                        <Link href={`/dashboard/shared/${project.id}`}>
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </Link>
                      </DropdownMenuItem>
                      {project.preview_url && (
                        <DropdownMenuItem asChild>
                          <a href={project.preview_url} target="_blank" rel="noopener noreferrer">
                            <Globe className="h-4 w-4 mr-2" />
                            Live Preview
                          </a>
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem onClick={() => handleForkProject(project.id)}>
                        <GitFork className="h-4 w-4 mr-2" />
                        Fork Project
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleToggleStar(project.id)}>
                        <Star className="h-4 w-4 mr-2" />
                        {project.is_starred ? 'Unstar' : 'Star'}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                {/* Author */}
                <div className="flex items-center gap-2 mt-3">
                  <Avatar className="w-6 h-6">
                    <AvatarFallback className="text-xs">
                      {project.author.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-sm text-muted-foreground">
                    by {project.author.name}
                  </span>
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                {/* Tags */}
                <div className="flex flex-wrap gap-1 mb-4">
                  {project.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {project.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{project.tags.length - 3}
                    </Badge>
                  )}
                </div>

                {/* Stats */}
                <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
                  <div className="flex items-center gap-4">
                    <span className="flex items-center gap-1">
                      <Star className="h-3 w-3" />
                      {project.stars}
                    </span>
                    <span className="flex items-center gap-1">
                      <GitFork className="h-3 w-3" />
                      {project.forks}
                    </span>
                    <span className="flex items-center gap-1">
                      <Eye className="h-3 w-3" />
                      {project.views}
                    </span>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => handleToggleStar(project.id)}
                  >
                    <Star className={`h-3 w-3 mr-2 ${project.is_starred ? 'fill-current' : ''}`} />
                    {project.is_starred ? 'Starred' : 'Star'}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => handleForkProject(project.id)}
                    disabled={project.is_forked}
                  >
                    <GitFork className="h-3 w-3 mr-2" />
                    {project.is_forked ? 'Forked' : 'Fork'}
                  </Button>
                </div>

                {/* Date */}
                <div className="text-xs text-muted-foreground mt-3">
                  Updated {formatDate(project.updated_at)}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
