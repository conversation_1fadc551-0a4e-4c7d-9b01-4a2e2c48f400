"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/auth/protected-route.tsx":
/*!*************************************************!*\
  !*** ./src/components/auth/protected-route.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProtectedRoute: function() { return /* binding */ ProtectedRoute; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* __next_internal_client_entry_do_not_use__ ProtectedRoute,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nfunction ProtectedRoute(param) {\n    let { children, fallback } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isAuthenticated, token, getCurrentUser, isLoading } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkAuth = async ()=>{\n            console.log(\"Protected route check:\", {\n                token: !!token,\n                isAuthenticated\n            }) // Debug log\n            ;\n            if (token && isAuthenticated) {\n                // We have token and user data, all good\n                setIsChecking(false);\n                return;\n            }\n            if (token && !isAuthenticated) {\n                // We have a token but no user data, try to get current user\n                try {\n                    await getCurrentUser();\n                    setIsChecking(false);\n                } catch (error) {\n                    console.error(\"Failed to get current user:\", error) // Debug log\n                    ;\n                    // If getting user fails, redirect to login\n                    router.push(\"/auth/login\");\n                    return;\n                }\n            } else if (!token) {\n                // No token, redirect to login\n                console.log(\"No token found, redirecting to login\") // Debug log\n                ;\n                router.push(\"/auth/login\");\n                return;\n            }\n        };\n        checkAuth();\n    }, [\n        token,\n        isAuthenticated,\n        getCurrentUser,\n        router\n    ]);\n    // Show loading while checking authentication\n    if (isChecking || isLoading) {\n        return fallback || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-background\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin text-primary mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\auth\\\\protected-route.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\auth\\\\protected-route.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\auth\\\\protected-route.tsx\",\n                lineNumber: 55,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\auth\\\\protected-route.tsx\",\n            lineNumber: 54,\n            columnNumber: 9\n        }, this);\n    }\n    // If not authenticated after checking, don't render children\n    if (!isAuthenticated) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(ProtectedRoute, \"lYT5ogPRvllAmVFJ8A2yFXXJhCE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthStore\n    ];\n});\n_c = ProtectedRoute;\n// Hook for checking authentication status\nfunction useAuth() {\n    _s1();\n    const { isAuthenticated, user, token, logout } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    return {\n        isAuthenticated,\n        user,\n        token,\n        logout\n    };\n}\n_s1(useAuth, \"xsSez+ABDPF7GT2T8hWUnEQKcnQ=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthStore\n    ];\n});\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2F1dGgvcHJvdGVjdGVkLXJvdXRlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUUyQztBQUNBO0FBQ0E7QUFDTDtBQU8vQixTQUFTSyxlQUFlLEtBQTJDO1FBQTNDLEVBQUVDLFFBQVEsRUFBRUMsUUFBUSxFQUF1QixHQUEzQzs7SUFDN0IsTUFBTUMsU0FBU04sMERBQVNBO0lBQ3hCLE1BQU0sRUFBRU8sZUFBZSxFQUFFQyxLQUFLLEVBQUVDLGNBQWMsRUFBRUMsU0FBUyxFQUFFLEdBQUdULHlEQUFZQTtJQUMxRSxNQUFNLENBQUNVLFlBQVlDLGNBQWMsR0FBR2IsK0NBQVFBLENBQUM7SUFFN0NELGdEQUFTQSxDQUFDO1FBQ1IsTUFBTWUsWUFBWTtZQUNoQkMsUUFBUUMsR0FBRyxDQUFDLDBCQUEwQjtnQkFBRVAsT0FBTyxDQUFDLENBQUNBO2dCQUFPRDtZQUFnQixHQUFHLFlBQVk7O1lBRXZGLElBQUlDLFNBQVNELGlCQUFpQjtnQkFDNUIsd0NBQXdDO2dCQUN4Q0ssY0FBYztnQkFDZDtZQUNGO1lBRUEsSUFBSUosU0FBUyxDQUFDRCxpQkFBaUI7Z0JBQzdCLDREQUE0RDtnQkFDNUQsSUFBSTtvQkFDRixNQUFNRTtvQkFDTkcsY0FBYztnQkFDaEIsRUFBRSxPQUFPSSxPQUFPO29CQUNkRixRQUFRRSxLQUFLLENBQUMsK0JBQStCQSxPQUFPLFlBQVk7O29CQUNoRSwyQ0FBMkM7b0JBQzNDVixPQUFPVyxJQUFJLENBQUM7b0JBQ1o7Z0JBQ0Y7WUFDRixPQUFPLElBQUksQ0FBQ1QsT0FBTztnQkFDakIsOEJBQThCO2dCQUM5Qk0sUUFBUUMsR0FBRyxDQUFDLHdDQUF3QyxZQUFZOztnQkFDaEVULE9BQU9XLElBQUksQ0FBQztnQkFDWjtZQUNGO1FBQ0Y7UUFFQUo7SUFDRixHQUFHO1FBQUNMO1FBQU9EO1FBQWlCRTtRQUFnQkg7S0FBTztJQUVuRCw2Q0FBNkM7SUFDN0MsSUFBSUssY0FBY0QsV0FBVztRQUMzQixPQUNFTCwwQkFDRSw4REFBQ2E7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDakIsbUZBQU9BO3dCQUFDaUIsV0FBVTs7Ozs7O2tDQUNuQiw4REFBQ0M7d0JBQUVELFdBQVU7a0NBQXdCOzs7Ozs7Ozs7Ozs7Ozs7OztJQUsvQztJQUVBLDZEQUE2RDtJQUM3RCxJQUFJLENBQUNaLGlCQUFpQjtRQUNwQixPQUFPO0lBQ1Q7SUFFQSxxQkFBTztrQkFBR0g7O0FBQ1o7R0F6RGdCRDs7UUFDQ0gsc0RBQVNBO1FBQ3NDQyxxREFBWUE7OztLQUY1REU7QUEyRGhCLDBDQUEwQztBQUNuQyxTQUFTa0I7O0lBQ2QsTUFBTSxFQUFFZCxlQUFlLEVBQUVlLElBQUksRUFBRWQsS0FBSyxFQUFFZSxNQUFNLEVBQUUsR0FBR3RCLHlEQUFZQTtJQUU3RCxPQUFPO1FBQ0xNO1FBQ0FlO1FBQ0FkO1FBQ0FlO0lBQ0Y7QUFDRjtJQVRnQkY7O1FBQ21DcEIscURBQVlBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL2F1dGgvcHJvdGVjdGVkLXJvdXRlLnRzeD9mYjY1Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG5pbXBvcnQgeyB1c2VBdXRoU3RvcmUgfSBmcm9tICdAL3N0b3JlL2F1dGgnXG5pbXBvcnQgeyBMb2FkZXIyIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5pbnRlcmZhY2UgUHJvdGVjdGVkUm91dGVQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbiAgZmFsbGJhY2s/OiBSZWFjdC5SZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFByb3RlY3RlZFJvdXRlKHsgY2hpbGRyZW4sIGZhbGxiYWNrIH06IFByb3RlY3RlZFJvdXRlUHJvcHMpIHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKClcbiAgY29uc3QgeyBpc0F1dGhlbnRpY2F0ZWQsIHRva2VuLCBnZXRDdXJyZW50VXNlciwgaXNMb2FkaW5nIH0gPSB1c2VBdXRoU3RvcmUoKVxuICBjb25zdCBbaXNDaGVja2luZywgc2V0SXNDaGVja2luZ10gPSB1c2VTdGF0ZSh0cnVlKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgY2hlY2tBdXRoID0gYXN5bmMgKCkgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ1Byb3RlY3RlZCByb3V0ZSBjaGVjazonLCB7IHRva2VuOiAhIXRva2VuLCBpc0F1dGhlbnRpY2F0ZWQgfSkgLy8gRGVidWcgbG9nXG5cbiAgICAgIGlmICh0b2tlbiAmJiBpc0F1dGhlbnRpY2F0ZWQpIHtcbiAgICAgICAgLy8gV2UgaGF2ZSB0b2tlbiBhbmQgdXNlciBkYXRhLCBhbGwgZ29vZFxuICAgICAgICBzZXRJc0NoZWNraW5nKGZhbHNlKVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cblxuICAgICAgaWYgKHRva2VuICYmICFpc0F1dGhlbnRpY2F0ZWQpIHtcbiAgICAgICAgLy8gV2UgaGF2ZSBhIHRva2VuIGJ1dCBubyB1c2VyIGRhdGEsIHRyeSB0byBnZXQgY3VycmVudCB1c2VyXG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgYXdhaXQgZ2V0Q3VycmVudFVzZXIoKVxuICAgICAgICAgIHNldElzQ2hlY2tpbmcoZmFsc2UpXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGdldCBjdXJyZW50IHVzZXI6JywgZXJyb3IpIC8vIERlYnVnIGxvZ1xuICAgICAgICAgIC8vIElmIGdldHRpbmcgdXNlciBmYWlscywgcmVkaXJlY3QgdG8gbG9naW5cbiAgICAgICAgICByb3V0ZXIucHVzaCgnL2F1dGgvbG9naW4nKVxuICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKCF0b2tlbikge1xuICAgICAgICAvLyBObyB0b2tlbiwgcmVkaXJlY3QgdG8gbG9naW5cbiAgICAgICAgY29uc29sZS5sb2coJ05vIHRva2VuIGZvdW5kLCByZWRpcmVjdGluZyB0byBsb2dpbicpIC8vIERlYnVnIGxvZ1xuICAgICAgICByb3V0ZXIucHVzaCgnL2F1dGgvbG9naW4nKVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cbiAgICB9XG5cbiAgICBjaGVja0F1dGgoKVxuICB9LCBbdG9rZW4sIGlzQXV0aGVudGljYXRlZCwgZ2V0Q3VycmVudFVzZXIsIHJvdXRlcl0pXG5cbiAgLy8gU2hvdyBsb2FkaW5nIHdoaWxlIGNoZWNraW5nIGF1dGhlbnRpY2F0aW9uXG4gIGlmIChpc0NoZWNraW5nIHx8IGlzTG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICBmYWxsYmFjayB8fCAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLWJhY2tncm91bmRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8TG9hZGVyMiBjbGFzc05hbWU9XCJoLTggdy04IGFuaW1hdGUtc3BpbiB0ZXh0LXByaW1hcnkgbXgtYXV0byBtYi00XCIgLz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPkxvYWRpbmcuLi48L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKVxuICAgIClcbiAgfVxuXG4gIC8vIElmIG5vdCBhdXRoZW50aWNhdGVkIGFmdGVyIGNoZWNraW5nLCBkb24ndCByZW5kZXIgY2hpbGRyZW5cbiAgaWYgKCFpc0F1dGhlbnRpY2F0ZWQpIHtcbiAgICByZXR1cm4gbnVsbFxuICB9XG5cbiAgcmV0dXJuIDw+e2NoaWxkcmVufTwvPlxufVxuXG4vLyBIb29rIGZvciBjaGVja2luZyBhdXRoZW50aWNhdGlvbiBzdGF0dXNcbmV4cG9ydCBmdW5jdGlvbiB1c2VBdXRoKCkge1xuICBjb25zdCB7IGlzQXV0aGVudGljYXRlZCwgdXNlciwgdG9rZW4sIGxvZ291dCB9ID0gdXNlQXV0aFN0b3JlKClcblxuICByZXR1cm4ge1xuICAgIGlzQXV0aGVudGljYXRlZCxcbiAgICB1c2VyLFxuICAgIHRva2VuLFxuICAgIGxvZ291dCxcbiAgfVxufVxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVN0YXRlIiwidXNlUm91dGVyIiwidXNlQXV0aFN0b3JlIiwiTG9hZGVyMiIsIlByb3RlY3RlZFJvdXRlIiwiY2hpbGRyZW4iLCJmYWxsYmFjayIsInJvdXRlciIsImlzQXV0aGVudGljYXRlZCIsInRva2VuIiwiZ2V0Q3VycmVudFVzZXIiLCJpc0xvYWRpbmciLCJpc0NoZWNraW5nIiwic2V0SXNDaGVja2luZyIsImNoZWNrQXV0aCIsImNvbnNvbGUiLCJsb2ciLCJlcnJvciIsInB1c2giLCJkaXYiLCJjbGFzc05hbWUiLCJwIiwidXNlQXV0aCIsInVzZXIiLCJsb2dvdXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/protected-route.tsx\n"));

/***/ })

});