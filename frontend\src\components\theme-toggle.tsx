'use client'

import * as React from 'react'
import { Moon, Sun } from 'lucide-react'
import { useTheme } from '@/components/theme-provider'
import { Button } from '@/components/ui/button'

export function ThemeToggle() {
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = React.useState(false)
  const [themeReady, setThemeReady] = React.useState(false)

  React.useEffect(() => {
    // Check if theme script has run
    if (typeof window !== 'undefined') {
      setThemeReady(!!window.__THEME_READY__)
    }
    setMounted(true)
  }, [])

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light')
  }

  // Always render the button structure to avoid layout shift
  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={mounted ? toggleTheme : undefined}
      className="h-9 w-9 relative"
      title={mounted ? `Switch to ${theme === 'light' ? 'dark' : 'light'} mode` : 'Toggle theme'}
      disabled={!mounted}
    >
      {mounted && themeReady ? (
        <>
          <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
        </>
      ) : (
        <div className="h-4 w-4 bg-muted-foreground/20 rounded animate-pulse" />
      )}
      <span className="sr-only">Toggle theme</span>
    </Button>
  )
}
