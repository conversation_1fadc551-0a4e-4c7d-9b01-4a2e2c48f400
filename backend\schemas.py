"""
Pydantic schemas for request/response models.
"""

from pydantic import BaseModel, EmailStr
from typing import Optional, List, Dict, Any
from datetime import datetime


# User schemas
class UserBase(BaseModel):
    full_name: str
    username: str
    email: EmailStr


class UserCreate(UserBase):
    password: str


class UserLogin(BaseModel):
    username_or_email: str
    password: str


class User(UserBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True


# Project schemas
class ProjectBase(BaseModel):
    name: str
    description: Optional[str] = None


class ProjectCreate(ProjectBase):
    pass


class ProjectUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    is_public: Optional[bool] = None


class Project(ProjectBase):
    id: int
    user_id: int
    is_public: bool
    share_token: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Message schemas
class MessageBase(BaseModel):
    content: str
    role: str


class MessageCreate(MessageBase):
    project_id: int
    version_id: Optional[int] = None
    parent_message_id: Optional[int] = None


class Message(MessageBase):
    id: int
    project_id: int
    version_id: Optional[int]
    parent_message_id: Optional[int]
    created_at: datetime

    class Config:
        from_attributes = True


# File schemas
class FileBase(BaseModel):
    path: str
    content: Optional[str] = None
    file_type: Optional[str] = None


class File(FileBase):
    id: int
    version_id: int
    created_at: datetime

    class Config:
        from_attributes = True


# Version schemas
class ProjectVersionBase(BaseModel):
    version_number: int
    description: Optional[str] = None


class ProjectVersion(ProjectVersionBase):
    id: int
    project_id: int
    trigger_message_id: Optional[int]
    created_at: datetime
    files: List[File] = []

    class Config:
        from_attributes = True


# Tool execution schemas
class ToolExecutionBase(BaseModel):
    tool_name: str
    args: Dict[str, Any]
    result: str


class ToolExecution(ToolExecutionBase):
    id: int
    message_id: int
    created_at: datetime

    class Config:
        from_attributes = True


# Authentication schemas
class Token(BaseModel):
    access_token: str
    token_type: str
    user: User


class TokenData(BaseModel):
    username: Optional[str] = None


# Chat schemas
class ChatMessage(BaseModel):
    content: str


class ChatResponse(BaseModel):
    message: Message
    tool_executions: List[ToolExecution] = []
    files_created: List[File] = []
    version_created: Optional[ProjectVersion] = None
