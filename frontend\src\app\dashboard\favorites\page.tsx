'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Search,
  Star,
  MoreVertical,
  Eye,
  GitFork,
  Download,
  Globe,
  Lock,
  Calendar,
  User,
  FolderOpen,
  MessageSquare,
  GitBranch,
  Heart,
} from 'lucide-react'

interface FavoriteProject {
  id: number
  name: string
  description: string
  type: 'own' | 'shared'
  author?: {
    name: string
    username: string
  }
  created_at: string
  updated_at: string
  starred_at: string
  is_public: boolean
  stats: {
    versions?: number
    messages?: number
    stars?: number
    forks?: number
  }
  tags?: string[]
}

export default function FavoritesPage() {
  const [favorites, setFavorites] = useState<FavoriteProject[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [activeTab, setActiveTab] = useState('all')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Mock favorites data
    const mockFavorites: FavoriteProject[] = [
      {
        id: 1,
        name: 'My Portfolio Website',
        description: 'Personal portfolio with modern design and animations',
        type: 'own',
        created_at: '2024-01-15T10:30:00Z',
        updated_at: '2024-01-20T14:45:00Z',
        starred_at: '2024-01-20T15:00:00Z',
        is_public: false,
        stats: {
          versions: 5,
          messages: 23
        }
      },
      {
        id: 4,
        name: 'Blog Website',
        description: 'Personal blog with CMS integration',
        type: 'own',
        created_at: '2024-01-01T08:45:00Z',
        updated_at: '2024-01-08T10:15:00Z',
        starred_at: '2024-01-08T11:00:00Z',
        is_public: true,
        stats: {
          versions: 6,
          messages: 28
        }
      },
      {
        id: 102,
        name: 'Creative Portfolio Showcase',
        description: 'A stunning portfolio website for creative professionals with smooth animations and modern design.',
        type: 'shared',
        author: {
          name: 'Alex Chen',
          username: 'alexc',
        },
        created_at: '2024-01-12T09:15:00Z',
        updated_at: '2024-01-18T16:20:00Z',
        starred_at: '2024-01-18T17:00:00Z',
        is_public: true,
        stats: {
          stars: 189,
          forks: 43
        },
        tags: ['portfolio', 'creative', 'animations']
      },
      {
        id: 105,
        name: 'Blog Template',
        description: 'Clean and minimal blog template with article management, categories, and responsive design.',
        type: 'shared',
        author: {
          name: 'David Kim',
          username: 'davidk',
        },
        created_at: '2024-01-01T08:30:00Z',
        updated_at: '2024-01-08T10:15:00Z',
        starred_at: '2024-01-09T09:30:00Z',
        is_public: true,
        stats: {
          stars: 134,
          forks: 56
        },
        tags: ['blog', 'minimal', 'cms']
      }
    ]

    setTimeout(() => {
      setFavorites(mockFavorites)
      setIsLoading(false)
    }, 1000)
  }, [])

  const filteredFavorites = favorites.filter(project => {
    const matchesSearch = 
      project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (project.tags && project.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))) ||
      (project.author && project.author.name.toLowerCase().includes(searchQuery.toLowerCase()))

    const matchesTab = 
      activeTab === 'all' ||
      (activeTab === 'own' && project.type === 'own') ||
      (activeTab === 'shared' && project.type === 'shared')

    return matchesSearch && matchesTab
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const handleRemoveFromFavorites = (projectId: number) => {
    setFavorites(prev => prev.filter(project => project.id !== projectId))
  }

  const ownProjects = filteredFavorites.filter(p => p.type === 'own')
  const sharedProjects = filteredFavorites.filter(p => p.type === 'shared')

  const renderProjectCard = (project: FavoriteProject) => (
    <Card key={project.id} className="group hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <CardTitle className="text-lg truncate">
                <Link 
                  href={project.type === 'own' 
                    ? `/dashboard/projects/${project.id}` 
                    : `/dashboard/shared/${project.id}`
                  }
                  className="hover:text-primary transition-colors"
                >
                  {project.name}
                </Link>
              </CardTitle>
              <Star className="h-4 w-4 text-yellow-500 fill-current" />
            </div>
            <CardDescription className="line-clamp-2">
              {project.description}
            </CardDescription>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link href={project.type === 'own' 
                  ? `/dashboard/projects/${project.id}` 
                  : `/dashboard/shared/${project.id}`
                }>
                  <Eye className="h-4 w-4 mr-2" />
                  View Project
                </Link>
              </DropdownMenuItem>
              {project.type === 'shared' && (
                <DropdownMenuItem>
                  <GitFork className="h-4 w-4 mr-2" />
                  Fork Project
                </DropdownMenuItem>
              )}
              <DropdownMenuItem>
                <Download className="h-4 w-4 mr-2" />
                Download
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => handleRemoveFromFavorites(project.id)}
                className="text-destructive"
              >
                <Star className="h-4 w-4 mr-2" />
                Remove from Favorites
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Author for shared projects */}
        {project.type === 'shared' && project.author && (
          <div className="flex items-center gap-2 mt-2">
            <Avatar className="w-5 h-5">
              <AvatarFallback className="text-xs">
                {project.author.name.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm text-muted-foreground">
              by {project.author.name}
            </span>
          </div>
        )}
      </CardHeader>

      <CardContent className="pt-0">
        {/* Tags for shared projects */}
        {project.tags && (
          <div className="flex flex-wrap gap-1 mb-4">
            {project.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
            {project.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{project.tags.length - 3}
              </Badge>
            )}
          </div>
        )}

        {/* Stats */}
        <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
          <div className="flex items-center gap-4">
            {project.type === 'own' ? (
              <>
                <span className="flex items-center gap-1">
                  <GitBranch className="h-3 w-3" />
                  {project.stats.versions}
                </span>
                <span className="flex items-center gap-1">
                  <MessageSquare className="h-3 w-3" />
                  {project.stats.messages}
                </span>
              </>
            ) : (
              <>
                <span className="flex items-center gap-1">
                  <Star className="h-3 w-3" />
                  {project.stats.stars}
                </span>
                <span className="flex items-center gap-1">
                  <GitFork className="h-3 w-3" />
                  {project.stats.forks}
                </span>
              </>
            )}
          </div>
          <Badge variant={project.is_public ? 'default' : 'secondary'}>
            {project.is_public ? (
              <>
                <Globe className="h-3 w-3 mr-1" />
                Public
              </>
            ) : (
              <>
                <Lock className="h-3 w-3 mr-1" />
                Private
              </>
            )}
          </Badge>
        </div>

        {/* Dates */}
        <div className="text-xs text-muted-foreground space-y-1">
          <div className="flex items-center justify-between">
            <span>Updated {formatDate(project.updated_at)}</span>
            <span className="flex items-center gap-1">
              <Heart className="h-3 w-3 text-red-500" />
              Starred {formatDate(project.starred_at)}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Favorites</h1>
          <p className="text-muted-foreground mt-2">
            Your starred projects and templates
          </p>
        </div>
      </div>

      {/* Search */}
      <div className="relative max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search favorites..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="all">
            All ({favorites.length})
          </TabsTrigger>
          <TabsTrigger value="own">
            My Projects ({ownProjects.length})
          </TabsTrigger>
          <TabsTrigger value="shared">
            Shared ({sharedProjects.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-6">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardHeader>
                    <div className="h-4 bg-muted rounded w-3/4" />
                    <div className="h-3 bg-muted rounded w-full" />
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="h-3 bg-muted rounded w-1/2" />
                      <div className="h-3 bg-muted rounded w-1/3" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredFavorites.length === 0 ? (
            <div className="text-center py-12">
              <Star className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">
                {searchQuery ? 'No favorites found' : 'No favorites yet'}
              </h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery 
                  ? 'Try adjusting your search query'
                  : 'Star projects to add them to your favorites'
                }
              </p>
              {!searchQuery && (
                <div className="flex gap-2 justify-center">
                  <Link href="/dashboard/projects">
                    <Button variant="outline">
                      <FolderOpen className="h-4 w-4 mr-2" />
                      Browse My Projects
                    </Button>
                  </Link>
                  <Link href="/dashboard/shared">
                    <Button>
                      <Globe className="h-4 w-4 mr-2" />
                      Explore Shared Projects
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredFavorites.map(renderProjectCard)}
            </div>
          )}
        </TabsContent>

        <TabsContent value="own" className="mt-6">
          {ownProjects.length === 0 ? (
            <div className="text-center py-12">
              <FolderOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">
                No favorite projects
              </h3>
              <p className="text-muted-foreground mb-4">
                Star your own projects to see them here
              </p>
              <Link href="/dashboard/projects">
                <Button>
                  <FolderOpen className="h-4 w-4 mr-2" />
                  View My Projects
                </Button>
              </Link>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {ownProjects.map(renderProjectCard)}
            </div>
          )}
        </TabsContent>

        <TabsContent value="shared" className="mt-6">
          {sharedProjects.length === 0 ? (
            <div className="text-center py-12">
              <Globe className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">
                No favorite shared projects
              </h3>
              <p className="text-muted-foreground mb-4">
                Star shared projects from the community to see them here
              </p>
              <Link href="/dashboard/shared">
                <Button>
                  <Globe className="h-4 w-4 mr-2" />
                  Explore Shared Projects
                </Button>
              </Link>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {sharedProjects.map(renderProjectCard)}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
