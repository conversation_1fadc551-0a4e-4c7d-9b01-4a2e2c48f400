"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-hook-form";
exports.ids = ["vendor-chunks/react-hook-form"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/react-hook-form/dist/index.esm.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   FormProvider: () => (/* binding */ FormProvider),\n/* harmony export */   appendErrors: () => (/* binding */ appendErrors),\n/* harmony export */   createFormControl: () => (/* binding */ createFormControl),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   useController: () => (/* binding */ useController),\n/* harmony export */   useFieldArray: () => (/* binding */ useFieldArray),\n/* harmony export */   useForm: () => (/* binding */ useForm),\n/* harmony export */   useFormContext: () => (/* binding */ useFormContext),\n/* harmony export */   useFormState: () => (/* binding */ useFormState),\n/* harmony export */   useWatch: () => (/* binding */ useWatch)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\nvar isCheckBoxInput = (element)=>element.type === \"checkbox\";\nvar isDateObject = (value1)=>value1 instanceof Date;\nvar isNullOrUndefined = (value1)=>value1 == null;\nconst isObjectType = (value1)=>typeof value1 === \"object\";\nvar isObject = (value1)=>!isNullOrUndefined(value1) && !Array.isArray(value1) && isObjectType(value1) && !isDateObject(value1);\nvar getEventValue = (event)=>isObject(event) && event.target ? isCheckBoxInput(event.target) ? event.target.checked : event.target.value : event;\nvar getNodeParentName = (name)=>name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\nvar isNameInFieldArray = (names, name)=>names.has(getNodeParentName(name));\nvar isPlainObject = (tempObject)=>{\n    const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n    return isObject(prototypeCopy) && prototypeCopy.hasOwnProperty(\"isPrototypeOf\");\n};\nvar isWeb =  false && 0;\nfunction cloneObject(data) {\n    let copy;\n    const isArray = Array.isArray(data);\n    const isFileListInstance = typeof FileList !== \"undefined\" ? data instanceof FileList : false;\n    if (data instanceof Date) {\n        copy = new Date(data);\n    } else if (data instanceof Set) {\n        copy = new Set(data);\n    } else if (!(isWeb && (data instanceof Blob || isFileListInstance)) && (isArray || isObject(data))) {\n        copy = isArray ? [] : {};\n        if (!isArray && !isPlainObject(data)) {\n            copy = data;\n        } else {\n            for(const key in data){\n                if (data.hasOwnProperty(key)) {\n                    copy[key] = cloneObject(data[key]);\n                }\n            }\n        }\n    } else {\n        return data;\n    }\n    return copy;\n}\nvar compact = (value1)=>Array.isArray(value1) ? value1.filter(Boolean) : [];\nvar isUndefined = (val)=>val === undefined;\nvar get = (object, path, defaultValue)=>{\n    if (!path || !isObject(object)) {\n        return defaultValue;\n    }\n    const result = compact(path.split(/[,[\\].]+?/)).reduce((result, key)=>isNullOrUndefined(result) ? result : result[key], object);\n    return isUndefined(result) || result === object ? isUndefined(object[path]) ? defaultValue : object[path] : result;\n};\nvar isBoolean = (value1)=>typeof value1 === \"boolean\";\nvar isKey = (value1)=>/^\\w*$/.test(value1);\nvar stringToPath = (input)=>compact(input.replace(/[\"|']|\\]/g, \"\").split(/\\.|\\[/));\nvar set = (object, path, value1)=>{\n    let index = -1;\n    const tempPath = isKey(path) ? [\n        path\n    ] : stringToPath(path);\n    const length = tempPath.length;\n    const lastIndex = length - 1;\n    while(++index < length){\n        const key = tempPath[index];\n        let newValue = value1;\n        if (index !== lastIndex) {\n            const objValue = object[key];\n            newValue = isObject(objValue) || Array.isArray(objValue) ? objValue : !isNaN(+tempPath[index + 1]) ? [] : {};\n        }\n        if (key === \"__proto__\" || key === \"constructor\" || key === \"prototype\") {\n            return;\n        }\n        object[key] = newValue;\n        object = object[key];\n    }\n};\nconst EVENTS = {\n    BLUR: \"blur\",\n    FOCUS_OUT: \"focusout\",\n    CHANGE: \"change\"\n};\nconst VALIDATION_MODE = {\n    onBlur: \"onBlur\",\n    onChange: \"onChange\",\n    onSubmit: \"onSubmit\",\n    onTouched: \"onTouched\",\n    all: \"all\"\n};\nconst INPUT_VALIDATION_RULES = {\n    max: \"max\",\n    min: \"min\",\n    maxLength: \"maxLength\",\n    minLength: \"minLength\",\n    pattern: \"pattern\",\n    required: \"required\",\n    validate: \"validate\"\n};\nconst HookFormContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */ const useFormContext = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */ const FormProvider = (props)=>{\n    const { children, ...data } = props;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(HookFormContext.Provider, {\n        value: data\n    }, children);\n};\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true)=>{\n    const result = {\n        defaultValues: control._defaultValues\n    };\n    for(const key in formState){\n        Object.defineProperty(result, key, {\n            get: ()=>{\n                const _key = key;\n                if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n                    control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n                }\n                localProxyFormState && (localProxyFormState[_key] = true);\n                return formState[_key];\n            }\n        });\n    }\n    return result;\n};\nconst useIsomorphicLayoutEffect =  false ? 0 : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */ function useFormState(props) {\n    const methods = useFormContext();\n    const { control = methods.control, disabled, name, exact } = props || {};\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._formState);\n    const _localProxyFormState = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        isDirty: false,\n        isLoading: false,\n        dirtyFields: false,\n        touchedFields: false,\n        validatingFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false\n    });\n    useIsomorphicLayoutEffect(()=>control._subscribe({\n            name: name,\n            formState: _localProxyFormState.current,\n            exact,\n            callback: (formState)=>{\n                !disabled && updateFormState({\n                    ...control._formState,\n                    ...formState\n                });\n            }\n        }), [\n        name,\n        disabled,\n        exact\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        _localProxyFormState.current.isValid && control._setValid(true);\n    }, [\n        control\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>getProxyFormState(formState, control, _localProxyFormState.current, false), [\n        formState,\n        control\n    ]);\n}\nvar isString = (value1)=>typeof value1 === \"string\";\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue)=>{\n    if (isString(names)) {\n        isGlobal && _names.watch.add(names);\n        return get(formValues, names, defaultValue);\n    }\n    if (Array.isArray(names)) {\n        return names.map((fieldName)=>(isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)));\n    }\n    isGlobal && (_names.watchAll = true);\n    return formValues;\n};\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */ function useWatch(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, defaultValue, disabled, exact } = props || {};\n    const _defaultValue = react__WEBPACK_IMPORTED_MODULE_0__.useRef(defaultValue);\n    const [value1, updateValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getWatch(name, _defaultValue.current));\n    useIsomorphicLayoutEffect(()=>control._subscribe({\n            name: name,\n            formState: {\n                values: true\n            },\n            exact,\n            callback: (formState)=>!disabled && updateValue(generateWatchOutput(name, control._names, formState.values || control._formValues, false, _defaultValue.current))\n        }), [\n        name,\n        control,\n        disabled,\n        exact\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._removeUnmounted());\n    return value1;\n}\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */ function useController(props) {\n    const methods = useFormContext();\n    const { name, disabled, control = methods.control, shouldUnregister } = props;\n    const isArrayField = isNameInFieldArray(control._names.array, name);\n    const value1 = useWatch({\n        control,\n        name,\n        defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n        exact: true\n    });\n    const formState = useFormState({\n        control,\n        name,\n        exact: true\n    });\n    const _props = react__WEBPACK_IMPORTED_MODULE_0__.useRef(props);\n    const _registerProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control.register(name, {\n        ...props.rules,\n        value: value1,\n        ...isBoolean(props.disabled) ? {\n            disabled: props.disabled\n        } : {}\n    }));\n    const fieldState = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>Object.defineProperties({}, {\n            invalid: {\n                enumerable: true,\n                get: ()=>!!get(formState.errors, name)\n            },\n            isDirty: {\n                enumerable: true,\n                get: ()=>!!get(formState.dirtyFields, name)\n            },\n            isTouched: {\n                enumerable: true,\n                get: ()=>!!get(formState.touchedFields, name)\n            },\n            isValidating: {\n                enumerable: true,\n                get: ()=>!!get(formState.validatingFields, name)\n            },\n            error: {\n                enumerable: true,\n                get: ()=>get(formState.errors, name)\n            }\n        }), [\n        formState,\n        name\n    ]);\n    const onChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>_registerProps.current.onChange({\n            target: {\n                value: getEventValue(event),\n                name: name\n            },\n            type: EVENTS.CHANGE\n        }), [\n        name\n    ]);\n    const onBlur = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>_registerProps.current.onBlur({\n            target: {\n                value: get(control._formValues, name),\n                name: name\n            },\n            type: EVENTS.BLUR\n        }), [\n        name,\n        control._formValues\n    ]);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((elm)=>{\n        const field = get(control._fields, name);\n        if (field && elm) {\n            field._f.ref = {\n                focus: ()=>elm.focus(),\n                select: ()=>elm.select(),\n                setCustomValidity: (message)=>elm.setCustomValidity(message),\n                reportValidity: ()=>elm.reportValidity()\n            };\n        }\n    }, [\n        control._fields,\n        name\n    ]);\n    const field = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            name,\n            value: value1,\n            ...isBoolean(disabled) || formState.disabled ? {\n                disabled: formState.disabled || disabled\n            } : {},\n            onChange,\n            onBlur,\n            ref\n        }), [\n        name,\n        disabled,\n        formState.disabled,\n        onChange,\n        onBlur,\n        ref,\n        value1\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n        control.register(name, {\n            ..._props.current.rules,\n            ...isBoolean(_props.current.disabled) ? {\n                disabled: _props.current.disabled\n            } : {}\n        });\n        const updateMounted = (name, value1)=>{\n            const field = get(control._fields, name);\n            if (field && field._f) {\n                field._f.mount = value1;\n            }\n        };\n        updateMounted(name, true);\n        if (_shouldUnregisterField) {\n            const value1 = cloneObject(get(control._options.defaultValues, name));\n            set(control._defaultValues, name, value1);\n            if (isUndefined(get(control._formValues, name))) {\n                set(control._formValues, name, value1);\n            }\n        }\n        !isArrayField && control.register(name);\n        return ()=>{\n            (isArrayField ? _shouldUnregisterField && !control._state.action : _shouldUnregisterField) ? control.unregister(name) : updateMounted(name, false);\n        };\n    }, [\n        name,\n        control,\n        isArrayField,\n        shouldUnregister\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        control._setDisabledField({\n            disabled,\n            name\n        });\n    }, [\n        disabled,\n        name,\n        control\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            field,\n            formState,\n            fieldState\n        }), [\n        field,\n        formState,\n        fieldState\n    ]);\n}\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */ const Controller = (props)=>props.render(useController(props));\nconst flatten = (obj)=>{\n    const output = {};\n    for (const key of Object.keys(obj)){\n        if (isObjectType(obj[key]) && obj[key] !== null) {\n            const nested = flatten(obj[key]);\n            for (const nestedKey of Object.keys(nested)){\n                output[`${key}.${nestedKey}`] = nested[nestedKey];\n            }\n        } else {\n            output[key] = obj[key];\n        }\n    }\n    return output;\n};\nconst POST_REQUEST = \"post\";\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */ function Form(props) {\n    const methods = useFormContext();\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const { control = methods.control, onSubmit, children, action, method = POST_REQUEST, headers, encType, onError, render, onSuccess, validateStatus, ...rest } = props;\n    const submit = async (event)=>{\n        let hasError = false;\n        let type = \"\";\n        await control.handleSubmit(async (data)=>{\n            const formData = new FormData();\n            let formDataJson = \"\";\n            try {\n                formDataJson = JSON.stringify(data);\n            } catch (_a) {}\n            const flattenFormValues = flatten(control._formValues);\n            for(const key in flattenFormValues){\n                formData.append(key, flattenFormValues[key]);\n            }\n            if (onSubmit) {\n                await onSubmit({\n                    data,\n                    event,\n                    method,\n                    formData,\n                    formDataJson\n                });\n            }\n            if (action) {\n                try {\n                    const shouldStringifySubmissionData = [\n                        headers && headers[\"Content-Type\"],\n                        encType\n                    ].some((value1)=>value1 && value1.includes(\"json\"));\n                    const response = await fetch(String(action), {\n                        method,\n                        headers: {\n                            ...headers,\n                            ...encType ? {\n                                \"Content-Type\": encType\n                            } : {}\n                        },\n                        body: shouldStringifySubmissionData ? formDataJson : formData\n                    });\n                    if (response && (validateStatus ? !validateStatus(response.status) : response.status < 200 || response.status >= 300)) {\n                        hasError = true;\n                        onError && onError({\n                            response\n                        });\n                        type = String(response.status);\n                    } else {\n                        onSuccess && onSuccess({\n                            response\n                        });\n                    }\n                } catch (error) {\n                    hasError = true;\n                    onError && onError({\n                        error\n                    });\n                }\n            }\n        })(event);\n        if (hasError && props.control) {\n            props.control._subjects.state.next({\n                isSubmitSuccessful: false\n            });\n            props.control.setError(\"root.server\", {\n                type\n            });\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        setMounted(true);\n    }, []);\n    return render ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render({\n        submit\n    })) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"form\", {\n        noValidate: mounted,\n        action: action,\n        method: method,\n        encType: encType,\n        onSubmit: submit,\n        ...rest\n    }, children);\n}\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message)=>validateAllFieldCriteria ? {\n        ...errors[name],\n        types: {\n            ...errors[name] && errors[name].types ? errors[name].types : {},\n            [type]: message || true\n        }\n    } : {};\nvar convertToArrayPayload = (value1)=>Array.isArray(value1) ? value1 : [\n        value1\n    ];\nvar createSubject = ()=>{\n    let _observers = [];\n    const next = (value1)=>{\n        for (const observer of _observers){\n            observer.next && observer.next(value1);\n        }\n    };\n    const subscribe = (observer)=>{\n        _observers.push(observer);\n        return {\n            unsubscribe: ()=>{\n                _observers = _observers.filter((o)=>o !== observer);\n            }\n        };\n    };\n    const unsubscribe = ()=>{\n        _observers = [];\n    };\n    return {\n        get observers () {\n            return _observers;\n        },\n        next,\n        subscribe,\n        unsubscribe\n    };\n};\nvar isPrimitive = (value1)=>isNullOrUndefined(value1) || !isObjectType(value1);\nfunction deepEqual(object1, object2) {\n    if (isPrimitive(object1) || isPrimitive(object2)) {\n        return object1 === object2;\n    }\n    if (isDateObject(object1) && isDateObject(object2)) {\n        return object1.getTime() === object2.getTime();\n    }\n    const keys1 = Object.keys(object1);\n    const keys2 = Object.keys(object2);\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    for (const key of keys1){\n        const val1 = object1[key];\n        if (!keys2.includes(key)) {\n            return false;\n        }\n        if (key !== \"ref\") {\n            const val2 = object2[key];\n            if (isDateObject(val1) && isDateObject(val2) || isObject(val1) && isObject(val2) || Array.isArray(val1) && Array.isArray(val2) ? !deepEqual(val1, val2) : val1 !== val2) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\nvar isEmptyObject = (value1)=>isObject(value1) && !Object.keys(value1).length;\nvar isFileInput = (element)=>element.type === \"file\";\nvar isFunction = (value1)=>typeof value1 === \"function\";\nvar isHTMLElement = (value1)=>{\n    if (!isWeb) {\n        return false;\n    }\n    const owner = value1 ? value1.ownerDocument : 0;\n    return value1 instanceof (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement);\n};\nvar isMultipleSelect = (element)=>element.type === `select-multiple`;\nvar isRadioInput = (element)=>element.type === \"radio\";\nvar isRadioOrCheckbox = (ref)=>isRadioInput(ref) || isCheckBoxInput(ref);\nvar live = (ref)=>isHTMLElement(ref) && ref.isConnected;\nfunction baseGet(object, updatePath) {\n    const length = updatePath.slice(0, -1).length;\n    let index = 0;\n    while(index < length){\n        object = isUndefined(object) ? index++ : object[updatePath[index++]];\n    }\n    return object;\n}\nfunction isEmptyArray(obj) {\n    for(const key in obj){\n        if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction unset(object, path) {\n    const paths = Array.isArray(path) ? path : isKey(path) ? [\n        path\n    ] : stringToPath(path);\n    const childObject = paths.length === 1 ? object : baseGet(object, paths);\n    const index = paths.length - 1;\n    const key = paths[index];\n    if (childObject) {\n        delete childObject[key];\n    }\n    if (index !== 0 && (isObject(childObject) && isEmptyObject(childObject) || Array.isArray(childObject) && isEmptyArray(childObject))) {\n        unset(object, paths.slice(0, -1));\n    }\n    return object;\n}\nvar objectHasFunction = (data)=>{\n    for(const key in data){\n        if (isFunction(data[key])) {\n            return true;\n        }\n    }\n    return false;\n};\nfunction markFieldsDirty(data, fields = {}) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for(const key in data){\n            if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n                fields[key] = Array.isArray(data[key]) ? [] : {};\n                markFieldsDirty(data[key], fields[key]);\n            } else if (!isNullOrUndefined(data[key])) {\n                fields[key] = true;\n            }\n        }\n    }\n    return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for(const key in data){\n            if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n                if (isUndefined(formValues) || isPrimitive(dirtyFieldsFromValues[key])) {\n                    dirtyFieldsFromValues[key] = Array.isArray(data[key]) ? markFieldsDirty(data[key], []) : {\n                        ...markFieldsDirty(data[key])\n                    };\n                } else {\n                    getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n                }\n            } else {\n                dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n            }\n        }\n    }\n    return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues)=>getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\nconst defaultResult = {\n    value: false,\n    isValid: false\n};\nconst validResult = {\n    value: true,\n    isValid: true\n};\nvar getCheckboxValue = (options)=>{\n    if (Array.isArray(options)) {\n        if (options.length > 1) {\n            const values = options.filter((option)=>option && option.checked && !option.disabled).map((option)=>option.value);\n            return {\n                value: values,\n                isValid: !!values.length\n            };\n        }\n        return options[0].checked && !options[0].disabled ? options[0].attributes && !isUndefined(options[0].attributes.value) ? isUndefined(options[0].value) || options[0].value === \"\" ? validResult : {\n            value: options[0].value,\n            isValid: true\n        } : validResult : defaultResult;\n    }\n    return defaultResult;\n};\nvar getFieldValueAs = (value1, { valueAsNumber, valueAsDate, setValueAs })=>isUndefined(value1) ? value1 : valueAsNumber ? value1 === \"\" ? NaN : value1 ? +value1 : value1 : valueAsDate && isString(value1) ? new Date(value1) : setValueAs ? setValueAs(value1) : value1;\nconst defaultReturn = {\n    isValid: false,\n    value: null\n};\nvar getRadioValue = (options)=>Array.isArray(options) ? options.reduce((previous, option)=>option && option.checked && !option.disabled ? {\n            isValid: true,\n            value: option.value\n        } : previous, defaultReturn) : defaultReturn;\nfunction getFieldValue(_f) {\n    const ref = _f.ref;\n    if (isFileInput(ref)) {\n        return ref.files;\n    }\n    if (isRadioInput(ref)) {\n        return getRadioValue(_f.refs).value;\n    }\n    if (isMultipleSelect(ref)) {\n        return [\n            ...ref.selectedOptions\n        ].map(({ value: value1 })=>value1);\n    }\n    if (isCheckBoxInput(ref)) {\n        return getCheckboxValue(_f.refs).value;\n    }\n    return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation)=>{\n    const fields = {};\n    for (const name of fieldsNames){\n        const field = get(_fields, name);\n        field && set(fields, name, field._f);\n    }\n    return {\n        criteriaMode,\n        names: [\n            ...fieldsNames\n        ],\n        fields,\n        shouldUseNativeValidation\n    };\n};\nvar isRegex = (value1)=>value1 instanceof RegExp;\nvar getRuleValue = (rule)=>isUndefined(rule) ? rule : isRegex(rule) ? rule.source : isObject(rule) ? isRegex(rule.value) ? rule.value.source : rule.value : rule;\nvar getValidationModes = (mode)=>({\n        isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n        isOnBlur: mode === VALIDATION_MODE.onBlur,\n        isOnChange: mode === VALIDATION_MODE.onChange,\n        isOnAll: mode === VALIDATION_MODE.all,\n        isOnTouch: mode === VALIDATION_MODE.onTouched\n    });\nconst ASYNC_FUNCTION = \"AsyncFunction\";\nvar hasPromiseValidation = (fieldReference)=>!!fieldReference && !!fieldReference.validate && !!(isFunction(fieldReference.validate) && fieldReference.validate.constructor.name === ASYNC_FUNCTION || isObject(fieldReference.validate) && Object.values(fieldReference.validate).find((validateFunction)=>validateFunction.constructor.name === ASYNC_FUNCTION));\nvar hasValidation = (options)=>options.mount && (options.required || options.min || options.max || options.maxLength || options.minLength || options.pattern || options.validate);\nvar isWatched = (name, _names, isBlurEvent)=>!isBlurEvent && (_names.watchAll || _names.watch.has(name) || [\n        ..._names.watch\n    ].some((watchName)=>name.startsWith(watchName) && /^\\.\\w+/.test(name.slice(watchName.length))));\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly)=>{\n    for (const key of fieldsNames || Object.keys(fields)){\n        const field = get(fields, key);\n        if (field) {\n            const { _f, ...currentField } = field;\n            if (_f) {\n                if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n                    return true;\n                } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n                    return true;\n                } else {\n                    if (iterateFieldsByAction(currentField, action)) {\n                        break;\n                    }\n                }\n            } else if (isObject(currentField)) {\n                if (iterateFieldsByAction(currentField, action)) {\n                    break;\n                }\n            }\n        }\n    }\n    return;\n};\nfunction schemaErrorLookup(errors, _fields, name) {\n    const error = get(errors, name);\n    if (error || isKey(name)) {\n        return {\n            error,\n            name\n        };\n    }\n    const names = name.split(\".\");\n    while(names.length){\n        const fieldName = names.join(\".\");\n        const field = get(_fields, fieldName);\n        const foundError = get(errors, fieldName);\n        if (field && !Array.isArray(field) && name !== fieldName) {\n            return {\n                name\n            };\n        }\n        if (foundError && foundError.type) {\n            return {\n                name: fieldName,\n                error: foundError\n            };\n        }\n        names.pop();\n    }\n    return {\n        name\n    };\n}\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot)=>{\n    updateFormState(formStateData);\n    const { name, ...formState } = formStateData;\n    return isEmptyObject(formState) || Object.keys(formState).length >= Object.keys(_proxyFormState).length || Object.keys(formState).find((key)=>_proxyFormState[key] === (!isRoot || VALIDATION_MODE.all));\n};\nvar shouldSubscribeByName = (name, signalName, exact)=>!name || !signalName || name === signalName || convertToArrayPayload(name).some((currentName)=>currentName && (exact ? currentName === signalName : currentName.startsWith(signalName) || signalName.startsWith(currentName)));\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode)=>{\n    if (mode.isOnAll) {\n        return false;\n    } else if (!isSubmitted && mode.isOnTouch) {\n        return !(isTouched || isBlurEvent);\n    } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n        return !isBlurEvent;\n    } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n        return isBlurEvent;\n    }\n    return true;\n};\nvar unsetEmptyArray = (ref, name)=>!compact(get(ref, name)).length && unset(ref, name);\nvar updateFieldArrayRootError = (errors, error, name)=>{\n    const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n    set(fieldArrayErrors, \"root\", error[name]);\n    set(errors, name, fieldArrayErrors);\n    return errors;\n};\nvar isMessage = (value1)=>isString(value1);\nfunction getValidateError(result, ref, type = \"validate\") {\n    if (isMessage(result) || Array.isArray(result) && result.every(isMessage) || isBoolean(result) && !result) {\n        return {\n            type,\n            message: isMessage(result) ? result : \"\",\n            ref\n        };\n    }\n}\nvar getValueAndMessage = (validationData)=>isObject(validationData) && !isRegex(validationData) ? validationData : {\n        value: validationData,\n        message: \"\"\n    };\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray)=>{\n    const { ref, refs, required, maxLength, minLength, min, max, pattern, validate, name, valueAsNumber, mount } = field._f;\n    const inputValue = get(formValues, name);\n    if (!mount || disabledFieldNames.has(name)) {\n        return {};\n    }\n    const inputRef = refs ? refs[0] : ref;\n    const setCustomValidity = (message)=>{\n        if (shouldUseNativeValidation && inputRef.reportValidity) {\n            inputRef.setCustomValidity(isBoolean(message) ? \"\" : message || \"\");\n            inputRef.reportValidity();\n        }\n    };\n    const error = {};\n    const isRadio = isRadioInput(ref);\n    const isCheckBox = isCheckBoxInput(ref);\n    const isRadioOrCheckbox = isRadio || isCheckBox;\n    const isEmpty = (valueAsNumber || isFileInput(ref)) && isUndefined(ref.value) && isUndefined(inputValue) || isHTMLElement(ref) && ref.value === \"\" || inputValue === \"\" || Array.isArray(inputValue) && !inputValue.length;\n    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength)=>{\n        const message = exceedMax ? maxLengthMessage : minLengthMessage;\n        error[name] = {\n            type: exceedMax ? maxType : minType,\n            message,\n            ref,\n            ...appendErrorsCurry(exceedMax ? maxType : minType, message)\n        };\n    };\n    if (isFieldArray ? !Array.isArray(inputValue) || !inputValue.length : required && (!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue)) || isBoolean(inputValue) && !inputValue || isCheckBox && !getCheckboxValue(refs).isValid || isRadio && !getRadioValue(refs).isValid)) {\n        const { value: value1, message } = isMessage(required) ? {\n            value: !!required,\n            message: required\n        } : getValueAndMessage(required);\n        if (value1) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.required,\n                message,\n                ref: inputRef,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message)\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n        let exceedMax;\n        let exceedMin;\n        const maxOutput = getValueAndMessage(max);\n        const minOutput = getValueAndMessage(min);\n        if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n            const valueNumber = ref.valueAsNumber || (inputValue ? +inputValue : inputValue);\n            if (!isNullOrUndefined(maxOutput.value)) {\n                exceedMax = valueNumber > maxOutput.value;\n            }\n            if (!isNullOrUndefined(minOutput.value)) {\n                exceedMin = valueNumber < minOutput.value;\n            }\n        } else {\n            const valueDate = ref.valueAsDate || new Date(inputValue);\n            const convertTimeToDate = (time)=>new Date(new Date().toDateString() + \" \" + time);\n            const isTime = ref.type == \"time\";\n            const isWeek = ref.type == \"week\";\n            if (isString(maxOutput.value) && inputValue) {\n                exceedMax = isTime ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value) : isWeek ? inputValue > maxOutput.value : valueDate > new Date(maxOutput.value);\n            }\n            if (isString(minOutput.value) && inputValue) {\n                exceedMin = isTime ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value) : isWeek ? inputValue < minOutput.value : valueDate < new Date(minOutput.value);\n            }\n        }\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if ((maxLength || minLength) && !isEmpty && (isString(inputValue) || isFieldArray && Array.isArray(inputValue))) {\n        const maxLengthOutput = getValueAndMessage(maxLength);\n        const minLengthOutput = getValueAndMessage(minLength);\n        const exceedMax = !isNullOrUndefined(maxLengthOutput.value) && inputValue.length > +maxLengthOutput.value;\n        const exceedMin = !isNullOrUndefined(minLengthOutput.value) && inputValue.length < +minLengthOutput.value;\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if (pattern && !isEmpty && isString(inputValue)) {\n        const { value: patternValue, message } = getValueAndMessage(pattern);\n        if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.pattern,\n                message,\n                ref,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message)\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (validate) {\n        if (isFunction(validate)) {\n            const result = await validate(inputValue, formValues);\n            const validateError = getValidateError(result, inputRef);\n            if (validateError) {\n                error[name] = {\n                    ...validateError,\n                    ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message)\n                };\n                if (!validateAllFieldCriteria) {\n                    setCustomValidity(validateError.message);\n                    return error;\n                }\n            }\n        } else if (isObject(validate)) {\n            let validationResult = {};\n            for(const key in validate){\n                if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n                    break;\n                }\n                const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n                if (validateError) {\n                    validationResult = {\n                        ...validateError,\n                        ...appendErrorsCurry(key, validateError.message)\n                    };\n                    setCustomValidity(validateError.message);\n                    if (validateAllFieldCriteria) {\n                        error[name] = validationResult;\n                    }\n                }\n            }\n            if (!isEmptyObject(validationResult)) {\n                error[name] = {\n                    ref: inputRef,\n                    ...validationResult\n                };\n                if (!validateAllFieldCriteria) {\n                    return error;\n                }\n            }\n        }\n    }\n    setCustomValidity(true);\n    return error;\n};\nconst defaultOptions = {\n    mode: VALIDATION_MODE.onSubmit,\n    reValidateMode: VALIDATION_MODE.onChange,\n    shouldFocusError: true\n};\nfunction createFormControl(props = {}) {\n    let _options = {\n        ...defaultOptions,\n        ...props\n    };\n    let _formState = {\n        submitCount: 0,\n        isDirty: false,\n        isReady: false,\n        isLoading: isFunction(_options.defaultValues),\n        isValidating: false,\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        touchedFields: {},\n        dirtyFields: {},\n        validatingFields: {},\n        errors: _options.errors || {},\n        disabled: _options.disabled || false\n    };\n    const _fields = {};\n    let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values) ? cloneObject(_options.defaultValues || _options.values) || {} : {};\n    let _formValues = _options.shouldUnregister ? {} : cloneObject(_defaultValues);\n    let _state = {\n        action: false,\n        mount: false,\n        watch: false\n    };\n    let _names = {\n        mount: new Set(),\n        disabled: new Set(),\n        unMount: new Set(),\n        array: new Set(),\n        watch: new Set()\n    };\n    let delayErrorCallback;\n    let timer = 0;\n    const _proxyFormState = {\n        isDirty: false,\n        dirtyFields: false,\n        validatingFields: false,\n        touchedFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false\n    };\n    let _proxySubscribeFormState = {\n        ..._proxyFormState\n    };\n    const _subjects = {\n        array: createSubject(),\n        state: createSubject()\n    };\n    const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n    const debounce = (callback)=>(wait)=>{\n            clearTimeout(timer);\n            timer = setTimeout(callback, wait);\n        };\n    const _setValid = async (shouldUpdateValid)=>{\n        if (!_options.disabled && (_proxyFormState.isValid || _proxySubscribeFormState.isValid || shouldUpdateValid)) {\n            const isValid = _options.resolver ? isEmptyObject((await _runSchema()).errors) : await executeBuiltInValidation(_fields, true);\n            if (isValid !== _formState.isValid) {\n                _subjects.state.next({\n                    isValid\n                });\n            }\n        }\n    };\n    const _updateIsValidating = (names, isValidating)=>{\n        if (!_options.disabled && (_proxyFormState.isValidating || _proxyFormState.validatingFields || _proxySubscribeFormState.isValidating || _proxySubscribeFormState.validatingFields)) {\n            (names || Array.from(_names.mount)).forEach((name)=>{\n                if (name) {\n                    isValidating ? set(_formState.validatingFields, name, isValidating) : unset(_formState.validatingFields, name);\n                }\n            });\n            _subjects.state.next({\n                validatingFields: _formState.validatingFields,\n                isValidating: !isEmptyObject(_formState.validatingFields)\n            });\n        }\n    };\n    const _setFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true)=>{\n        if (args && method && !_options.disabled) {\n            _state.action = true;\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n                const fieldValues = method(get(_fields, name), args.argA, args.argB);\n                shouldSetValues && set(_fields, name, fieldValues);\n            }\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_formState.errors, name))) {\n                const errors = method(get(_formState.errors, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.errors, name, errors);\n                unsetEmptyArray(_formState.errors, name);\n            }\n            if ((_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && shouldUpdateFieldsAndState && Array.isArray(get(_formState.touchedFields, name))) {\n                const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n            }\n            if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n                _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n            }\n            _subjects.state.next({\n                name,\n                isDirty: _getDirty(name, values),\n                dirtyFields: _formState.dirtyFields,\n                errors: _formState.errors,\n                isValid: _formState.isValid\n            });\n        } else {\n            set(_formValues, name, values);\n        }\n    };\n    const updateErrors = (name, error)=>{\n        set(_formState.errors, name, error);\n        _subjects.state.next({\n            errors: _formState.errors\n        });\n    };\n    const _setErrors = (errors)=>{\n        _formState.errors = errors;\n        _subjects.state.next({\n            errors: _formState.errors,\n            isValid: false\n        });\n    };\n    const updateValidAndValue = (name, shouldSkipSetValueAs, value1, ref)=>{\n        const field = get(_fields, name);\n        if (field) {\n            const defaultValue = get(_formValues, name, isUndefined(value1) ? get(_defaultValues, name) : value1);\n            isUndefined(defaultValue) || ref && ref.defaultChecked || shouldSkipSetValueAs ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f)) : setFieldValue(name, defaultValue);\n            _state.mount && _setValid();\n        }\n    };\n    const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender)=>{\n        let shouldUpdateField = false;\n        let isPreviousDirty = false;\n        const output = {\n            name\n        };\n        if (!_options.disabled) {\n            if (!isBlurEvent || shouldDirty) {\n                if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n                    isPreviousDirty = _formState.isDirty;\n                    _formState.isDirty = output.isDirty = _getDirty();\n                    shouldUpdateField = isPreviousDirty !== output.isDirty;\n                }\n                const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);\n                isPreviousDirty = !!get(_formState.dirtyFields, name);\n                isCurrentFieldPristine ? unset(_formState.dirtyFields, name) : set(_formState.dirtyFields, name, true);\n                output.dirtyFields = _formState.dirtyFields;\n                shouldUpdateField = shouldUpdateField || (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) && isPreviousDirty !== !isCurrentFieldPristine;\n            }\n            if (isBlurEvent) {\n                const isPreviousFieldTouched = get(_formState.touchedFields, name);\n                if (!isPreviousFieldTouched) {\n                    set(_formState.touchedFields, name, isBlurEvent);\n                    output.touchedFields = _formState.touchedFields;\n                    shouldUpdateField = shouldUpdateField || (_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && isPreviousFieldTouched !== isBlurEvent;\n                }\n            }\n            shouldUpdateField && shouldRender && _subjects.state.next(output);\n        }\n        return shouldUpdateField ? output : {};\n    };\n    const shouldRenderByError = (name, isValid, error, fieldState)=>{\n        const previousFieldError = get(_formState.errors, name);\n        const shouldUpdateValid = (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isBoolean(isValid) && _formState.isValid !== isValid;\n        if (_options.delayError && error) {\n            delayErrorCallback = debounce(()=>updateErrors(name, error));\n            delayErrorCallback(_options.delayError);\n        } else {\n            clearTimeout(timer);\n            delayErrorCallback = null;\n            error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n        }\n        if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) || !isEmptyObject(fieldState) || shouldUpdateValid) {\n            const updatedFormState = {\n                ...fieldState,\n                ...shouldUpdateValid && isBoolean(isValid) ? {\n                    isValid\n                } : {},\n                errors: _formState.errors,\n                name\n            };\n            _formState = {\n                ..._formState,\n                ...updatedFormState\n            };\n            _subjects.state.next(updatedFormState);\n        }\n    };\n    const _runSchema = async (name)=>{\n        _updateIsValidating(name, true);\n        const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n        _updateIsValidating(name);\n        return result;\n    };\n    const executeSchemaAndUpdateState = async (names)=>{\n        const { errors } = await _runSchema(names);\n        if (names) {\n            for (const name of names){\n                const error = get(errors, name);\n                error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n            }\n        } else {\n            _formState.errors = errors;\n        }\n        return errors;\n    };\n    const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n        valid: true\n    })=>{\n        for(const name in fields){\n            const field = fields[name];\n            if (field) {\n                const { _f, ...fieldValue } = field;\n                if (_f) {\n                    const isFieldArrayRoot = _names.array.has(_f.name);\n                    const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([\n                            name\n                        ], true);\n                    }\n                    const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([\n                            name\n                        ]);\n                    }\n                    if (fieldError[_f.name]) {\n                        context.valid = false;\n                        if (shouldOnlyCheckValid) {\n                            break;\n                        }\n                    }\n                    !shouldOnlyCheckValid && (get(fieldError, _f.name) ? isFieldArrayRoot ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name) : set(_formState.errors, _f.name, fieldError[_f.name]) : unset(_formState.errors, _f.name));\n                }\n                !isEmptyObject(fieldValue) && await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context);\n            }\n        }\n        return context.valid;\n    };\n    const _removeUnmounted = ()=>{\n        for (const name of _names.unMount){\n            const field = get(_fields, name);\n            field && (field._f.refs ? field._f.refs.every((ref)=>!live(ref)) : !live(field._f.ref)) && unregister(name);\n        }\n        _names.unMount = new Set();\n    };\n    const _getDirty = (name, data)=>!_options.disabled && (name && data && set(_formValues, name, data), !deepEqual(getValues(), _defaultValues));\n    const _getWatch = (names, defaultValue, isGlobal)=>generateWatchOutput(names, _names, {\n            ..._state.mount ? _formValues : isUndefined(defaultValue) ? _defaultValues : isString(names) ? {\n                [names]: defaultValue\n            } : defaultValue\n        }, isGlobal, defaultValue);\n    const _getFieldArray = (name)=>compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n    const setFieldValue = (name, value1, options = {})=>{\n        const field = get(_fields, name);\n        let fieldValue = value1;\n        if (field) {\n            const fieldReference = field._f;\n            if (fieldReference) {\n                !fieldReference.disabled && set(_formValues, name, getFieldValueAs(value1, fieldReference));\n                fieldValue = isHTMLElement(fieldReference.ref) && isNullOrUndefined(value1) ? \"\" : value1;\n                if (isMultipleSelect(fieldReference.ref)) {\n                    [\n                        ...fieldReference.ref.options\n                    ].forEach((optionRef)=>optionRef.selected = fieldValue.includes(optionRef.value));\n                } else if (fieldReference.refs) {\n                    if (isCheckBoxInput(fieldReference.ref)) {\n                        fieldReference.refs.forEach((checkboxRef)=>{\n                            if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                                if (Array.isArray(fieldValue)) {\n                                    checkboxRef.checked = !!fieldValue.find((data)=>data === checkboxRef.value);\n                                } else {\n                                    checkboxRef.checked = fieldValue === checkboxRef.value || !!fieldValue;\n                                }\n                            }\n                        });\n                    } else {\n                        fieldReference.refs.forEach((radioRef)=>radioRef.checked = radioRef.value === fieldValue);\n                    }\n                } else if (isFileInput(fieldReference.ref)) {\n                    fieldReference.ref.value = \"\";\n                } else {\n                    fieldReference.ref.value = fieldValue;\n                    if (!fieldReference.ref.type) {\n                        _subjects.state.next({\n                            name,\n                            values: cloneObject(_formValues)\n                        });\n                    }\n                }\n            }\n        }\n        (options.shouldDirty || options.shouldTouch) && updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n        options.shouldValidate && trigger(name);\n    };\n    const setValues = (name, value1, options)=>{\n        for(const fieldKey in value1){\n            if (!value1.hasOwnProperty(fieldKey)) {\n                return;\n            }\n            const fieldValue = value1[fieldKey];\n            const fieldName = `${name}.${fieldKey}`;\n            const field = get(_fields, fieldName);\n            (_names.array.has(name) || isObject(fieldValue) || field && !field._f) && !isDateObject(fieldValue) ? setValues(fieldName, fieldValue, options) : setFieldValue(fieldName, fieldValue, options);\n        }\n    };\n    const setValue = (name, value1, options = {})=>{\n        const field = get(_fields, name);\n        const isFieldArray = _names.array.has(name);\n        const cloneValue = cloneObject(value1);\n        set(_formValues, name, cloneValue);\n        if (isFieldArray) {\n            _subjects.array.next({\n                name,\n                values: cloneObject(_formValues)\n            });\n            if ((_proxyFormState.isDirty || _proxyFormState.dirtyFields || _proxySubscribeFormState.isDirty || _proxySubscribeFormState.dirtyFields) && options.shouldDirty) {\n                _subjects.state.next({\n                    name,\n                    dirtyFields: getDirtyFields(_defaultValues, _formValues),\n                    isDirty: _getDirty(name, cloneValue)\n                });\n            }\n        } else {\n            field && !field._f && !isNullOrUndefined(cloneValue) ? setValues(name, cloneValue, options) : setFieldValue(name, cloneValue, options);\n        }\n        isWatched(name, _names) && _subjects.state.next({\n            ..._formState\n        });\n        _subjects.state.next({\n            name: _state.mount ? name : undefined,\n            values: cloneObject(_formValues)\n        });\n    };\n    const onChange = async (event)=>{\n        _state.mount = true;\n        const target = event.target;\n        let name = target.name;\n        let isFieldValueUpdated = true;\n        const field = get(_fields, name);\n        const _updateIsFieldValueUpdated = (fieldValue)=>{\n            isFieldValueUpdated = Number.isNaN(fieldValue) || isDateObject(fieldValue) && isNaN(fieldValue.getTime()) || deepEqual(fieldValue, get(_formValues, name, fieldValue));\n        };\n        const validationModeBeforeSubmit = getValidationModes(_options.mode);\n        const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n        if (field) {\n            let error;\n            let isValid;\n            const fieldValue = target.type ? getFieldValue(field._f) : getEventValue(event);\n            const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n            const shouldSkipValidation = !hasValidation(field._f) && !_options.resolver && !get(_formState.errors, name) && !field._f.deps || skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n            const watched = isWatched(name, _names, isBlurEvent);\n            set(_formValues, name, fieldValue);\n            if (isBlurEvent) {\n                field._f.onBlur && field._f.onBlur(event);\n                delayErrorCallback && delayErrorCallback(0);\n            } else if (field._f.onChange) {\n                field._f.onChange(event);\n            }\n            const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n            const shouldRender = !isEmptyObject(fieldState) || watched;\n            !isBlurEvent && _subjects.state.next({\n                name,\n                type: event.type,\n                values: cloneObject(_formValues)\n            });\n            if (shouldSkipValidation) {\n                if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n                    if (_options.mode === \"onBlur\") {\n                        if (isBlurEvent) {\n                            _setValid();\n                        }\n                    } else if (!isBlurEvent) {\n                        _setValid();\n                    }\n                }\n                return shouldRender && _subjects.state.next({\n                    name,\n                    ...watched ? {} : fieldState\n                });\n            }\n            !isBlurEvent && watched && _subjects.state.next({\n                ..._formState\n            });\n            if (_options.resolver) {\n                const { errors } = await _runSchema([\n                    name\n                ]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n                    const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n                    error = errorLookupResult.error;\n                    name = errorLookupResult.name;\n                    isValid = isEmptyObject(errors);\n                }\n            } else {\n                _updateIsValidating([\n                    name\n                ], true);\n                error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n                _updateIsValidating([\n                    name\n                ]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    if (error) {\n                        isValid = false;\n                    } else if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n                        isValid = await executeBuiltInValidation(_fields, true);\n                    }\n                }\n            }\n            if (isFieldValueUpdated) {\n                field._f.deps && trigger(field._f.deps);\n                shouldRenderByError(name, isValid, error, fieldState);\n            }\n        }\n    };\n    const _focusInput = (ref, key)=>{\n        if (get(_formState.errors, key) && ref.focus) {\n            ref.focus();\n            return 1;\n        }\n        return;\n    };\n    const trigger = async (name, options = {})=>{\n        let isValid;\n        let validationResult;\n        const fieldNames = convertToArrayPayload(name);\n        if (_options.resolver) {\n            const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n            isValid = isEmptyObject(errors);\n            validationResult = name ? !fieldNames.some((name)=>get(errors, name)) : isValid;\n        } else if (name) {\n            validationResult = (await Promise.all(fieldNames.map(async (fieldName)=>{\n                const field = get(_fields, fieldName);\n                return await executeBuiltInValidation(field && field._f ? {\n                    [fieldName]: field\n                } : field);\n            }))).every(Boolean);\n            !(!validationResult && !_formState.isValid) && _setValid();\n        } else {\n            validationResult = isValid = await executeBuiltInValidation(_fields);\n        }\n        _subjects.state.next({\n            ...!isString(name) || (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isValid !== _formState.isValid ? {} : {\n                name\n            },\n            ..._options.resolver || !name ? {\n                isValid\n            } : {},\n            errors: _formState.errors\n        });\n        options.shouldFocus && !validationResult && iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n        return validationResult;\n    };\n    const getValues = (fieldNames)=>{\n        const values = {\n            ..._state.mount ? _formValues : _defaultValues\n        };\n        return isUndefined(fieldNames) ? values : isString(fieldNames) ? get(values, fieldNames) : fieldNames.map((name)=>get(values, name));\n    };\n    const getFieldState = (name, formState)=>({\n            invalid: !!get((formState || _formState).errors, name),\n            isDirty: !!get((formState || _formState).dirtyFields, name),\n            error: get((formState || _formState).errors, name),\n            isValidating: !!get(_formState.validatingFields, name),\n            isTouched: !!get((formState || _formState).touchedFields, name)\n        });\n    const clearErrors = (name)=>{\n        name && convertToArrayPayload(name).forEach((inputName)=>unset(_formState.errors, inputName));\n        _subjects.state.next({\n            errors: name ? _formState.errors : {}\n        });\n    };\n    const setError = (name, error, options)=>{\n        const ref = (get(_fields, name, {\n            _f: {}\n        })._f || {}).ref;\n        const currentError = get(_formState.errors, name) || {};\n        // Don't override existing error messages elsewhere in the object tree.\n        const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n        set(_formState.errors, name, {\n            ...restOfErrorTree,\n            ...error,\n            ref\n        });\n        _subjects.state.next({\n            name,\n            errors: _formState.errors,\n            isValid: false\n        });\n        options && options.shouldFocus && ref && ref.focus && ref.focus();\n    };\n    const watch = (name, defaultValue)=>isFunction(name) ? _subjects.state.subscribe({\n            next: (payload)=>name(_getWatch(undefined, defaultValue), payload)\n        }) : _getWatch(name, defaultValue, true);\n    const _subscribe = (props)=>_subjects.state.subscribe({\n            next: (formState)=>{\n                if (shouldSubscribeByName(props.name, formState.name, props.exact) && shouldRenderFormState(formState, props.formState || _proxyFormState, _setFormState, props.reRenderRoot)) {\n                    props.callback({\n                        values: {\n                            ..._formValues\n                        },\n                        ..._formState,\n                        ...formState\n                    });\n                }\n            }\n        }).unsubscribe;\n    const subscribe = (props)=>{\n        _state.mount = true;\n        _proxySubscribeFormState = {\n            ..._proxySubscribeFormState,\n            ...props.formState\n        };\n        return _subscribe({\n            ...props,\n            formState: _proxySubscribeFormState\n        });\n    };\n    const unregister = (name, options = {})=>{\n        for (const fieldName of name ? convertToArrayPayload(name) : _names.mount){\n            _names.mount.delete(fieldName);\n            _names.array.delete(fieldName);\n            if (!options.keepValue) {\n                unset(_fields, fieldName);\n                unset(_formValues, fieldName);\n            }\n            !options.keepError && unset(_formState.errors, fieldName);\n            !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n            !options.keepTouched && unset(_formState.touchedFields, fieldName);\n            !options.keepIsValidating && unset(_formState.validatingFields, fieldName);\n            !_options.shouldUnregister && !options.keepDefaultValue && unset(_defaultValues, fieldName);\n        }\n        _subjects.state.next({\n            values: cloneObject(_formValues)\n        });\n        _subjects.state.next({\n            ..._formState,\n            ...!options.keepDirty ? {} : {\n                isDirty: _getDirty()\n            }\n        });\n        !options.keepIsValid && _setValid();\n    };\n    const _setDisabledField = ({ disabled, name })=>{\n        if (isBoolean(disabled) && _state.mount || !!disabled || _names.disabled.has(name)) {\n            disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n        }\n    };\n    const register = (name, options = {})=>{\n        let field = get(_fields, name);\n        const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n        set(_fields, name, {\n            ...field || {},\n            _f: {\n                ...field && field._f ? field._f : {\n                    ref: {\n                        name\n                    }\n                },\n                name,\n                mount: true,\n                ...options\n            }\n        });\n        _names.mount.add(name);\n        if (field) {\n            _setDisabledField({\n                disabled: isBoolean(options.disabled) ? options.disabled : _options.disabled,\n                name\n            });\n        } else {\n            updateValidAndValue(name, true, options.value);\n        }\n        return {\n            ...disabledIsDefined ? {\n                disabled: options.disabled || _options.disabled\n            } : {},\n            ..._options.progressive ? {\n                required: !!options.required,\n                min: getRuleValue(options.min),\n                max: getRuleValue(options.max),\n                minLength: getRuleValue(options.minLength),\n                maxLength: getRuleValue(options.maxLength),\n                pattern: getRuleValue(options.pattern)\n            } : {},\n            name,\n            onChange,\n            onBlur: onChange,\n            ref: (ref)=>{\n                if (ref) {\n                    register(name, options);\n                    field = get(_fields, name);\n                    const fieldRef = isUndefined(ref.value) ? ref.querySelectorAll ? ref.querySelectorAll(\"input,select,textarea\")[0] || ref : ref : ref;\n                    const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n                    const refs = field._f.refs || [];\n                    if (radioOrCheckbox ? refs.find((option)=>option === fieldRef) : fieldRef === field._f.ref) {\n                        return;\n                    }\n                    set(_fields, name, {\n                        _f: {\n                            ...field._f,\n                            ...radioOrCheckbox ? {\n                                refs: [\n                                    ...refs.filter(live),\n                                    fieldRef,\n                                    ...Array.isArray(get(_defaultValues, name)) ? [\n                                        {}\n                                    ] : []\n                                ],\n                                ref: {\n                                    type: fieldRef.type,\n                                    name\n                                }\n                            } : {\n                                ref: fieldRef\n                            }\n                        }\n                    });\n                    updateValidAndValue(name, false, undefined, fieldRef);\n                } else {\n                    field = get(_fields, name, {});\n                    if (field._f) {\n                        field._f.mount = false;\n                    }\n                    (_options.shouldUnregister || options.shouldUnregister) && !(isNameInFieldArray(_names.array, name) && _state.action) && _names.unMount.add(name);\n                }\n            }\n        };\n    };\n    const _focusError = ()=>_options.shouldFocusError && iterateFieldsByAction(_fields, _focusInput, _names.mount);\n    const _disableForm = (disabled)=>{\n        if (isBoolean(disabled)) {\n            _subjects.state.next({\n                disabled\n            });\n            iterateFieldsByAction(_fields, (ref, name)=>{\n                const currentField = get(_fields, name);\n                if (currentField) {\n                    ref.disabled = currentField._f.disabled || disabled;\n                    if (Array.isArray(currentField._f.refs)) {\n                        currentField._f.refs.forEach((inputRef)=>{\n                            inputRef.disabled = currentField._f.disabled || disabled;\n                        });\n                    }\n                }\n            }, 0, false);\n        }\n    };\n    const handleSubmit = (onValid, onInvalid)=>async (e)=>{\n            let onValidError = undefined;\n            if (e) {\n                e.preventDefault && e.preventDefault();\n                e.persist && e.persist();\n            }\n            let fieldValues = cloneObject(_formValues);\n            _subjects.state.next({\n                isSubmitting: true\n            });\n            if (_options.resolver) {\n                const { errors, values } = await _runSchema();\n                _formState.errors = errors;\n                fieldValues = values;\n            } else {\n                await executeBuiltInValidation(_fields);\n            }\n            if (_names.disabled.size) {\n                for (const name of _names.disabled){\n                    set(fieldValues, name, undefined);\n                }\n            }\n            unset(_formState.errors, \"root\");\n            if (isEmptyObject(_formState.errors)) {\n                _subjects.state.next({\n                    errors: {}\n                });\n                try {\n                    await onValid(fieldValues, e);\n                } catch (error) {\n                    onValidError = error;\n                }\n            } else {\n                if (onInvalid) {\n                    await onInvalid({\n                        ..._formState.errors\n                    }, e);\n                }\n                _focusError();\n                setTimeout(_focusError);\n            }\n            _subjects.state.next({\n                isSubmitted: true,\n                isSubmitting: false,\n                isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n                submitCount: _formState.submitCount + 1,\n                errors: _formState.errors\n            });\n            if (onValidError) {\n                throw onValidError;\n            }\n        };\n    const resetField = (name, options = {})=>{\n        if (get(_fields, name)) {\n            if (isUndefined(options.defaultValue)) {\n                setValue(name, cloneObject(get(_defaultValues, name)));\n            } else {\n                setValue(name, options.defaultValue);\n                set(_defaultValues, name, cloneObject(options.defaultValue));\n            }\n            if (!options.keepTouched) {\n                unset(_formState.touchedFields, name);\n            }\n            if (!options.keepDirty) {\n                unset(_formState.dirtyFields, name);\n                _formState.isDirty = options.defaultValue ? _getDirty(name, cloneObject(get(_defaultValues, name))) : _getDirty();\n            }\n            if (!options.keepError) {\n                unset(_formState.errors, name);\n                _proxyFormState.isValid && _setValid();\n            }\n            _subjects.state.next({\n                ..._formState\n            });\n        }\n    };\n    const _reset = (formValues, keepStateOptions = {})=>{\n        const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n        const cloneUpdatedValues = cloneObject(updatedValues);\n        const isEmptyResetValues = isEmptyObject(formValues);\n        const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n        if (!keepStateOptions.keepDefaultValues) {\n            _defaultValues = updatedValues;\n        }\n        if (!keepStateOptions.keepValues) {\n            if (keepStateOptions.keepDirtyValues) {\n                const fieldsToCheck = new Set([\n                    ..._names.mount,\n                    ...Object.keys(getDirtyFields(_defaultValues, _formValues))\n                ]);\n                for (const fieldName of Array.from(fieldsToCheck)){\n                    get(_formState.dirtyFields, fieldName) ? set(values, fieldName, get(_formValues, fieldName)) : setValue(fieldName, get(values, fieldName));\n                }\n            } else {\n                if (isWeb && isUndefined(formValues)) {\n                    for (const name of _names.mount){\n                        const field = get(_fields, name);\n                        if (field && field._f) {\n                            const fieldReference = Array.isArray(field._f.refs) ? field._f.refs[0] : field._f.ref;\n                            if (isHTMLElement(fieldReference)) {\n                                const form = fieldReference.closest(\"form\");\n                                if (form) {\n                                    form.reset();\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                }\n                for (const fieldName of _names.mount){\n                    setValue(fieldName, get(values, fieldName));\n                }\n            }\n            _formValues = cloneObject(values);\n            _subjects.array.next({\n                values: {\n                    ...values\n                }\n            });\n            _subjects.state.next({\n                values: {\n                    ...values\n                }\n            });\n        }\n        _names = {\n            mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n            unMount: new Set(),\n            array: new Set(),\n            disabled: new Set(),\n            watch: new Set(),\n            watchAll: false,\n            focus: \"\"\n        };\n        _state.mount = !_proxyFormState.isValid || !!keepStateOptions.keepIsValid || !!keepStateOptions.keepDirtyValues;\n        _state.watch = !!_options.shouldUnregister;\n        _subjects.state.next({\n            submitCount: keepStateOptions.keepSubmitCount ? _formState.submitCount : 0,\n            isDirty: isEmptyResetValues ? false : keepStateOptions.keepDirty ? _formState.isDirty : !!(keepStateOptions.keepDefaultValues && !deepEqual(formValues, _defaultValues)),\n            isSubmitted: keepStateOptions.keepIsSubmitted ? _formState.isSubmitted : false,\n            dirtyFields: isEmptyResetValues ? {} : keepStateOptions.keepDirtyValues ? keepStateOptions.keepDefaultValues && _formValues ? getDirtyFields(_defaultValues, _formValues) : _formState.dirtyFields : keepStateOptions.keepDefaultValues && formValues ? getDirtyFields(_defaultValues, formValues) : keepStateOptions.keepDirty ? _formState.dirtyFields : {},\n            touchedFields: keepStateOptions.keepTouched ? _formState.touchedFields : {},\n            errors: keepStateOptions.keepErrors ? _formState.errors : {},\n            isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful ? _formState.isSubmitSuccessful : false,\n            isSubmitting: false\n        });\n    };\n    const reset = (formValues, keepStateOptions)=>_reset(isFunction(formValues) ? formValues(_formValues) : formValues, keepStateOptions);\n    const setFocus = (name, options = {})=>{\n        const field = get(_fields, name);\n        const fieldReference = field && field._f;\n        if (fieldReference) {\n            const fieldRef = fieldReference.refs ? fieldReference.refs[0] : fieldReference.ref;\n            if (fieldRef.focus) {\n                fieldRef.focus();\n                options.shouldSelect && isFunction(fieldRef.select) && fieldRef.select();\n            }\n        }\n    };\n    const _setFormState = (updatedFormState)=>{\n        _formState = {\n            ..._formState,\n            ...updatedFormState\n        };\n    };\n    const _resetDefaultValues = ()=>isFunction(_options.defaultValues) && _options.defaultValues().then((values)=>{\n            reset(values, _options.resetOptions);\n            _subjects.state.next({\n                isLoading: false\n            });\n        });\n    const methods = {\n        control: {\n            register,\n            unregister,\n            getFieldState,\n            handleSubmit,\n            setError,\n            _subscribe,\n            _runSchema,\n            _getWatch,\n            _getDirty,\n            _setValid,\n            _setFieldArray,\n            _setDisabledField,\n            _setErrors,\n            _getFieldArray,\n            _reset,\n            _resetDefaultValues,\n            _removeUnmounted,\n            _disableForm,\n            _subjects,\n            _proxyFormState,\n            get _fields () {\n                return _fields;\n            },\n            get _formValues () {\n                return _formValues;\n            },\n            get _state () {\n                return _state;\n            },\n            set _state (value){\n                _state = value;\n            },\n            get _defaultValues () {\n                return _defaultValues;\n            },\n            get _names () {\n                return _names;\n            },\n            set _names (value){\n                _names = value;\n            },\n            get _formState () {\n                return _formState;\n            },\n            get _options () {\n                return _options;\n            },\n            set _options (value){\n                _options = {\n                    ..._options,\n                    ...value\n                };\n            }\n        },\n        subscribe,\n        trigger,\n        register,\n        handleSubmit,\n        watch,\n        setValue,\n        getValues,\n        reset,\n        resetField,\n        clearErrors,\n        unregister,\n        setError,\n        setFocus,\n        getFieldState\n    };\n    return {\n        ...methods,\n        formControl: methods\n    };\n}\nvar generateId = ()=>{\n    const d = typeof performance === \"undefined\" ? Date.now() : performance.now() * 1000;\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, (c)=>{\n        const r = (Math.random() * 16 + d) % 16 | 0;\n        return (c == \"x\" ? r : r & 0x3 | 0x8).toString(16);\n    });\n};\nvar getFocusFieldName = (name, index, options = {})=>options.shouldFocus || isUndefined(options.shouldFocus) ? options.focusName || `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.` : \"\";\nvar appendAt = (data, value1)=>[\n        ...data,\n        ...convertToArrayPayload(value1)\n    ];\nvar fillEmptyArray = (value1)=>Array.isArray(value1) ? value1.map(()=>undefined) : undefined;\nfunction insert(data, index, value1) {\n    return [\n        ...data.slice(0, index),\n        ...convertToArrayPayload(value1),\n        ...data.slice(index)\n    ];\n}\nvar moveArrayAt = (data, from, to)=>{\n    if (!Array.isArray(data)) {\n        return [];\n    }\n    if (isUndefined(data[to])) {\n        data[to] = undefined;\n    }\n    data.splice(to, 0, data.splice(from, 1)[0]);\n    return data;\n};\nvar prependAt = (data, value1)=>[\n        ...convertToArrayPayload(value1),\n        ...convertToArrayPayload(data)\n    ];\nfunction removeAtIndexes(data, indexes) {\n    let i = 0;\n    const temp = [\n        ...data\n    ];\n    for (const index of indexes){\n        temp.splice(index - i, 1);\n        i++;\n    }\n    return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index)=>isUndefined(index) ? [] : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b)=>a - b));\nvar swapArrayAt = (data, indexA, indexB)=>{\n    [data[indexA], data[indexB]] = [\n        data[indexB],\n        data[indexA]\n    ];\n};\nvar updateAt = (fieldValues, index, value1)=>{\n    fieldValues[index] = value1;\n    return fieldValues;\n};\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */ function useFieldArray(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, keyName = \"id\", shouldUnregister, rules } = props;\n    const [fields, setFields] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getFieldArray(name));\n    const ids = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control._getFieldArray(name).map(generateId));\n    const _fieldIds = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fields);\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    const _actioned = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    _name.current = name;\n    _fieldIds.current = fields;\n    control._names.array.add(name);\n    rules && control.register(name, rules);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._subjects.array.subscribe({\n            next: ({ values, name: fieldArrayName })=>{\n                if (fieldArrayName === _name.current || !fieldArrayName) {\n                    const fieldValues = get(values, _name.current);\n                    if (Array.isArray(fieldValues)) {\n                        setFields(fieldValues);\n                        ids.current = fieldValues.map(generateId);\n                    }\n                }\n            }\n        }).unsubscribe, [\n        control\n    ]);\n    const updateValues = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((updatedFieldArrayValues)=>{\n        _actioned.current = true;\n        control._setFieldArray(name, updatedFieldArrayValues);\n    }, [\n        control,\n        name\n    ]);\n    const append = (value1, options)=>{\n        const appendValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n        control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n        ids.current = appendAt(ids.current, appendValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n            argA: fillEmptyArray(value1)\n        });\n    };\n    const prepend = (value1, options)=>{\n        const prependValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n        control._names.focus = getFocusFieldName(name, 0, options);\n        ids.current = prependAt(ids.current, prependValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n            argA: fillEmptyArray(value1)\n        });\n    };\n    const remove = (index)=>{\n        const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n        ids.current = removeArrayAt(ids.current, index);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        !Array.isArray(get(control._fields, name)) && set(control._fields, name, undefined);\n        control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n            argA: index\n        });\n    };\n    const insert$1 = (index, value1, options)=>{\n        const insertValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n        control._names.focus = getFocusFieldName(name, index, options);\n        ids.current = insert(ids.current, index, insertValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, insert, {\n            argA: index,\n            argB: fillEmptyArray(value1)\n        });\n    };\n    const swap = (indexA, indexB)=>{\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n        swapArrayAt(ids.current, indexA, indexB);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n            argA: indexA,\n            argB: indexB\n        }, false);\n    };\n    const move = (from, to)=>{\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        moveArrayAt(updatedFieldArrayValues, from, to);\n        moveArrayAt(ids.current, from, to);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n            argA: from,\n            argB: to\n        }, false);\n    };\n    const update = (index, value1)=>{\n        const updateValue = cloneObject(value1);\n        const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n        ids.current = [\n            ...updatedFieldArrayValues\n        ].map((item, i)=>!item || i === index ? generateId() : ids.current[i]);\n        updateValues(updatedFieldArrayValues);\n        setFields([\n            ...updatedFieldArrayValues\n        ]);\n        control._setFieldArray(name, updatedFieldArrayValues, updateAt, {\n            argA: index,\n            argB: updateValue\n        }, true, false);\n    };\n    const replace = (value1)=>{\n        const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value1));\n        ids.current = updatedFieldArrayValues.map(generateId);\n        updateValues([\n            ...updatedFieldArrayValues\n        ]);\n        setFields([\n            ...updatedFieldArrayValues\n        ]);\n        control._setFieldArray(name, [\n            ...updatedFieldArrayValues\n        ], (data)=>data, {}, true, false);\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        control._state.action = false;\n        isWatched(name, control._names) && control._subjects.state.next({\n            ...control._formState\n        });\n        if (_actioned.current && (!getValidationModes(control._options.mode).isOnSubmit || control._formState.isSubmitted) && !getValidationModes(control._options.reValidateMode).isOnSubmit) {\n            if (control._options.resolver) {\n                control._runSchema([\n                    name\n                ]).then((result)=>{\n                    const error = get(result.errors, name);\n                    const existingError = get(control._formState.errors, name);\n                    if (existingError ? !error && existingError.type || error && (existingError.type !== error.type || existingError.message !== error.message) : error && error.type) {\n                        error ? set(control._formState.errors, name, error) : unset(control._formState.errors, name);\n                        control._subjects.state.next({\n                            errors: control._formState.errors\n                        });\n                    }\n                });\n            } else {\n                const field = get(control._fields, name);\n                if (field && field._f && !(getValidationModes(control._options.reValidateMode).isOnSubmit && getValidationModes(control._options.mode).isOnSubmit)) {\n                    validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then((error)=>!isEmptyObject(error) && control._subjects.state.next({\n                            errors: updateFieldArrayRootError(control._formState.errors, error, name)\n                        }));\n                }\n            }\n        }\n        control._subjects.state.next({\n            name,\n            values: cloneObject(control._formValues)\n        });\n        control._names.focus && iterateFieldsByAction(control._fields, (ref, key)=>{\n            if (control._names.focus && key.startsWith(control._names.focus) && ref.focus) {\n                ref.focus();\n                return 1;\n            }\n            return;\n        });\n        control._names.focus = \"\";\n        control._setValid();\n        _actioned.current = false;\n    }, [\n        fields,\n        name,\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        !get(control._formValues, name) && control._setFieldArray(name);\n        return ()=>{\n            const updateMounted = (name, value1)=>{\n                const field = get(control._fields, name);\n                if (field && field._f) {\n                    field._f.mount = value1;\n                }\n            };\n            control._options.shouldUnregister || shouldUnregister ? control.unregister(name) : updateMounted(name, false);\n        };\n    }, [\n        name,\n        control,\n        keyName,\n        shouldUnregister\n    ]);\n    return {\n        swap: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(swap, [\n            updateValues,\n            name,\n            control\n        ]),\n        move: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(move, [\n            updateValues,\n            name,\n            control\n        ]),\n        prepend: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(prepend, [\n            updateValues,\n            name,\n            control\n        ]),\n        append: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(append, [\n            updateValues,\n            name,\n            control\n        ]),\n        remove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(remove, [\n            updateValues,\n            name,\n            control\n        ]),\n        insert: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(insert$1, [\n            updateValues,\n            name,\n            control\n        ]),\n        update: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(update, [\n            updateValues,\n            name,\n            control\n        ]),\n        replace: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(replace, [\n            updateValues,\n            name,\n            control\n        ]),\n        fields: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>fields.map((field, index)=>({\n                    ...field,\n                    [keyName]: ids.current[index] || generateId()\n                })), [\n            fields,\n            keyName\n        ])\n    };\n}\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */ function useForm(props = {}) {\n    const _formControl = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const _values = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        isDirty: false,\n        isValidating: false,\n        isLoading: isFunction(props.defaultValues),\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        submitCount: 0,\n        dirtyFields: {},\n        touchedFields: {},\n        validatingFields: {},\n        errors: props.errors || {},\n        disabled: props.disabled || false,\n        isReady: false,\n        defaultValues: isFunction(props.defaultValues) ? undefined : props.defaultValues\n    });\n    if (!_formControl.current) {\n        _formControl.current = {\n            ...props.formControl ? props.formControl : createFormControl(props),\n            formState\n        };\n        if (props.formControl && props.defaultValues && !isFunction(props.defaultValues)) {\n            props.formControl.reset(props.defaultValues, props.resetOptions);\n        }\n    }\n    const control = _formControl.current.control;\n    control._options = props;\n    useIsomorphicLayoutEffect(()=>{\n        const sub = control._subscribe({\n            formState: control._proxyFormState,\n            callback: ()=>updateFormState({\n                    ...control._formState\n                }),\n            reRenderRoot: true\n        });\n        updateFormState((data)=>({\n                ...data,\n                isReady: true\n            }));\n        control._formState.isReady = true;\n        return sub;\n    }, [\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._disableForm(props.disabled), [\n        control,\n        props.disabled\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.mode) {\n            control._options.mode = props.mode;\n        }\n        if (props.reValidateMode) {\n            control._options.reValidateMode = props.reValidateMode;\n        }\n        if (props.errors && !isEmptyObject(props.errors)) {\n            control._setErrors(props.errors);\n        }\n    }, [\n        control,\n        props.errors,\n        props.mode,\n        props.reValidateMode\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        props.shouldUnregister && control._subjects.state.next({\n            values: control._getWatch()\n        });\n    }, [\n        control,\n        props.shouldUnregister\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (control._proxyFormState.isDirty) {\n            const isDirty = control._getDirty();\n            if (isDirty !== formState.isDirty) {\n                control._subjects.state.next({\n                    isDirty\n                });\n            }\n        }\n    }, [\n        control,\n        formState.isDirty\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.values && !deepEqual(props.values, _values.current)) {\n            control._reset(props.values, control._options.resetOptions);\n            _values.current = props.values;\n            updateFormState((state)=>({\n                    ...state\n                }));\n        } else {\n            control._resetDefaultValues();\n        }\n    }, [\n        control,\n        props.values\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!control._state.mount) {\n            control._setValid();\n            control._state.mount = true;\n        }\n        if (control._state.watch) {\n            control._state.watch = false;\n            control._subjects.state.next({\n                ...control._formState\n            });\n        }\n        control._removeUnmounted();\n    });\n    _formControl.current.formState = getProxyFormState(formState, control);\n    return _formControl.current;\n}\n //# sourceMappingURL=index.esm.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\n");

/***/ })

};
;