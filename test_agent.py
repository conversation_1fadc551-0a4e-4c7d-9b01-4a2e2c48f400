"""
Test script for the AI Agent functionality.
"""

import requests
import json
import time
import async<PERSON>
from typing import Dict, Any

BASE_URL = "http://localhost:8000"

def get_auth_token():
    """Get authentication token."""
    # Register a test user
    user_data = {
        "full_name": "Agent Test User",
        "username": "agenttest",
        "email": "<EMAIL>",
        "password": "testpassword123"
    }

    try:
        # Try to register (might fail if user exists)
        requests.post(f"{BASE_URL}/auth/register", json=user_data)
    except:
        pass

    # Login
    login_data = {
        "username_or_email": "agenttest",
        "password": "testpassword123"
    }

    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        return response.json()["access_token"]
    return None

def create_test_project(token):
    """Create a test project."""
    project_data = {
        "name": "AI Agent Test Project",
        "description": "Testing the AI agent functionality"
    }

    headers = {"Authorization": f"Bearer {token}"}
    response = requests.post(f"{BASE_URL}/projects/", json=project_data, headers=headers)

    if response.status_code == 200:
        return response.json()
    return None

def test_agent_chat(token, project_id):
    """Test the AI agent chat functionality."""
    print(f"\n🤖 Testing AI Agent Chat for Project {project_id}...")

    message_data = {
        "content": "Create a simple HTML page with a welcome message and some basic styling using Tailwind CSS."
    }

    headers = {"Authorization": f"Bearer {token}"}

    try:
        # Send message to agent
        response = requests.post(
            f"{BASE_URL}/chat/{project_id}/message",
            json=message_data,
            headers=headers,
            stream=True
        )

        if response.status_code == 200:
            print("✅ Agent chat request successful")
            print("📡 Streaming response:")

            # Process streaming response
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        try:
                            data = json.loads(line_str[6:])  # Remove 'data: ' prefix

                            if data.get('type') == 'message':
                                print(f"💬 Agent: {data.get('content', '')[:100]}...")
                            elif data.get('type') == 'tool_result':
                                print(f"🔧 Tool {data.get('tool_name')}: {data.get('result', '')[:50]}...")
                            elif data.get('type') == 'completion':
                                print(f"✅ Completed! Version: {data.get('version_id')}, Files: {data.get('files_count')}")
                                return data.get('version_id')
                            elif data.get('type') == 'error':
                                print(f"❌ Error: {data.get('error')}")
                                if data.get('details'):
                                    print(f"📋 Details: {data.get('details')}")
                                return None
                            elif data.get('type') == 'done':
                                print("🏁 Stream completed")
                                break
                        except json.JSONDecodeError:
                            continue

            return True
        else:
            print(f"❌ Agent chat failed: {response.status_code}")
            print(response.text)
            return None

    except Exception as e:
        print(f"❌ Agent chat error: {e}")
        return None

def test_version_preview(token, project_id, version_id):
    """Test version preview functionality."""
    print(f"\n📁 Testing Version Preview for Version {version_id}...")

    headers = {"Authorization": f"Bearer {token}"}

    try:
        response = requests.get(
            f"{BASE_URL}/chat/{project_id}/versions/{version_id}/preview",
            headers=headers
        )

        if response.status_code == 200:
            data = response.json()
            files = data.get('files', [])
            print(f"✅ Version preview successful - Found {len(files)} files:")

            for file in files:
                print(f"  📄 {file['path']} ({file.get('file_type', 'unknown')})")
                if file['path'].endswith('.html'):
                    print(f"    📝 Content preview: {file['content'][:100]}...")

            return True
        else:
            print(f"❌ Version preview failed: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Version preview error: {e}")
        return False

def test_project_history(token, project_id):
    """Test project chat history."""
    print(f"\n📜 Testing Project History for Project {project_id}...")

    headers = {"Authorization": f"Bearer {token}"}

    try:
        response = requests.get(f"{BASE_URL}/chat/{project_id}/history", headers=headers)

        if response.status_code == 200:
            data = response.json()
            messages = data.get('messages', [])
            print(f"✅ History retrieved - Found {len(messages)} messages:")

            for msg in messages:
                role = msg['role']
                content = msg['content'][:50] + "..." if len(msg['content']) > 50 else msg['content']
                print(f"  💬 {role}: {content}")

            return True
        else:
            print(f"❌ History retrieval failed: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ History retrieval error: {e}")
        return False

def main():
    """Run all agent tests."""
    print("🧪 Starting Codora AI Agent Tests...\n")

    # Get authentication token
    print("🔐 Getting authentication token...")
    token = get_auth_token()
    if not token:
        print("❌ Failed to get authentication token")
        return False
    print("✅ Authentication successful")

    # Create test project
    print("\n📁 Creating test project...")
    project = create_test_project(token)
    if not project:
        print("❌ Failed to create test project")
        return False

    project_id = project['id']
    print(f"✅ Project created: {project['name']} (ID: {project_id})")

    # Test agent chat
    version_id = test_agent_chat(token, project_id)
    if not version_id:
        print("❌ Agent chat test failed")
        return False

    # Test version preview
    if not test_version_preview(token, project_id, version_id):
        print("❌ Version preview test failed")
        return False

    # Test project history
    if not test_project_history(token, project_id):
        print("❌ Project history test failed")
        return False

    print("\n🎉 All AI Agent tests passed!")
    print("✅ Authentication working")
    print("✅ Project creation working")
    print("✅ AI Agent chat working")
    print("✅ Real-time streaming working")
    print("✅ File creation working")
    print("✅ Version management working")
    print("✅ History tracking working")

    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💡 Make sure:")
        print("1. Backend server is running (python run_backend.py)")
        print("2. Database is set up correctly")
        print("3. .env file has correct API keys")
        print("4. OpenAI API key is valid")
    exit(0 if success else 1)
