# Optional Docker Compose for development
# This is only for the database - the backend runs locally

version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: codora
      POSTGRES_USER: codora_user
      POSTGRES_PASSWORD: codora_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U codora_user -d codora"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
