"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/auth-buttons.tsx":
/*!*****************************************!*\
  !*** ./src/components/auth-buttons.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthButtons: function() { return /* binding */ AuthButtons; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _hooks_use_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/use-auth */ \"(app-pages-browser)/./src/hooks/use-auth.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthButtons auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AuthButtons(param) {\n    let { onGetStarted } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isLoggedIn, isHydrated } = (0,_hooks_use_auth__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [initialAuthState, setInitialAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get initial auth state from the script\n        if (true) {\n            setInitialAuthState(window.__INITIAL_AUTH_STATE__ || false);\n        }\n    }, []);\n    const handleGetStarted = ()=>{\n        if (onGetStarted) {\n            onGetStarted();\n        } else if (isLoggedIn || initialAuthState) {\n            router.push(\"/dashboard\");\n        } else {\n            router.push(\"/auth/register\");\n        }\n    };\n    // Use initial state if hydration hasn't completed yet\n    const currentAuthState = isHydrated ? isLoggedIn : initialAuthState;\n    // Show loading only if we don't have initial state\n    if (initialAuthState === null) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-20 h-9 bg-muted animate-pulse rounded-md\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\auth-buttons.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-24 h-9 bg-muted animate-pulse rounded-md\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\auth-buttons.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\auth-buttons.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this);\n    }\n    // Show appropriate buttons based on auth status\n    if (currentAuthState) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n            onClick: ()=>router.push(\"/dashboard\"),\n            children: \"Dashboard\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\auth-buttons.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                variant: \"ghost\",\n                onClick: ()=>router.push(\"/auth/login\"),\n                children: \"Sign In\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\auth-buttons.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                onClick: handleGetStarted,\n                children: \"Get Started\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\auth-buttons.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AuthButtons, \"DHo9+5LABeD8pqq93gn4wV3Q7cU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_auth__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c = AuthButtons;\nvar _c;\n$RefreshReg$(_c, \"AuthButtons\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth-buttons.tsx\n"));

/***/ })

});