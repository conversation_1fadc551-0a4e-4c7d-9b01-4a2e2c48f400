#!/usr/bin/env python3
"""
Database setup script for Codora.
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"Error: {e.stderr}")
        return False

def main():
    """Main setup function."""
    print("🚀 Setting up Codora Database...")
    
    # Check if .env file exists
    if not Path(".env").exists():
        print("\n⚠️  .env file not found. Please create one based on .env.example")
        print("Make sure to set the correct DATABASE_URL for PostgreSQL")
        return False
    
    # Install dependencies
    if not run_command("pip install -r requirements.txt", "Installing dependencies"):
        return False
    
    # Initialize Alembic (only if not already initialized)
    if not Path("alembic/versions").exists():
        if not run_command("alembic init alembic", "Initializing Alembic"):
            return False
    
    # Create initial migration
    if not run_command("alembic revision --autogenerate -m 'Initial migration'", "Creating initial migration"):
        return False
    
    # Run migrations
    if not run_command("alembic upgrade head", "Running database migrations"):
        return False
    
    print("\n🎉 Database setup completed successfully!")
    print("\nNext steps:")
    print("1. Start the backend server: uvicorn backend.main:app --reload")
    print("2. The API will be available at: http://localhost:8000")
    print("3. API documentation at: http://localhost:8000/docs")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
