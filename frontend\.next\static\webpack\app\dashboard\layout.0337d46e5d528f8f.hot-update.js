"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/auth/protected-route.tsx":
/*!*************************************************!*\
  !*** ./src/components/auth/protected-route.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProtectedRoute: function() { return /* binding */ ProtectedRoute; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-auth */ \"(app-pages-browser)/./src/hooks/use-auth.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* __next_internal_client_entry_do_not_use__ ProtectedRoute auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ProtectedRoute(param) {\n    let { children, fallback } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isLoggedIn, isHydrated, isReady, token, getCurrentUser, isLoading } = (0,_hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isHydrated) return; // Don't check auth until hydrated\n        const checkAuth = async ()=>{\n            console.log(\"Protected route check:\", {\n                token: !!token,\n                isLoggedIn,\n                isHydrated,\n                isReady\n            }) // Debug log\n            ;\n            if (isLoggedIn) {\n                // User is authenticated and hydrated\n                console.log(\"Auth check passed - user authenticated\") // Debug log\n                ;\n                setIsChecking(false);\n                return;\n            }\n            if (token && !isLoggedIn) {\n                // We have a token but user is not authenticated, try to get current user\n                console.log(\"Token found but not authenticated, getting current user\") // Debug log\n                ;\n                try {\n                    await getCurrentUser();\n                    setIsChecking(false);\n                } catch (error) {\n                    console.error(\"Failed to get current user:\", error) // Debug log\n                    ;\n                    // If getting user fails, redirect to login\n                    router.push(\"/auth/login\");\n                    return;\n                }\n            } else if (!token) {\n                // No token, redirect to login\n                console.log(\"No token found, redirecting to login\") // Debug log\n                ;\n                router.push(\"/auth/login\");\n                return;\n            }\n        };\n        checkAuth();\n    }, [\n        token,\n        isLoggedIn,\n        isHydrated,\n        isReady,\n        getCurrentUser,\n        router\n    ]);\n    // Show loading while checking authentication\n    if (isChecking || isLoading) {\n        return fallback || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-background\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin text-primary mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\auth\\\\protected-route.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\auth\\\\protected-route.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\auth\\\\protected-route.tsx\",\n                lineNumber: 64,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\auth\\\\protected-route.tsx\",\n            lineNumber: 63,\n            columnNumber: 9\n        }, this);\n    }\n    // If not authenticated after checking, don't render children\n    if (!isLoggedIn) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(ProtectedRoute, \"Tl00Rw3YJQItJpj0D69+Bel1kms=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/protected-route.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/use-auth.ts":
/*!*******************************!*\
  !*** ./src/hooks/use-auth.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth auto */ \n\nfunction useAuth() {\n    const store = (0,_store_auth__WEBPACK_IMPORTED_MODULE_1__.useAuthStore)();\n    const [isHydrated, setIsHydrated] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Check if we're on the client side and store has hydrated\n        const checkHydration = ()=>{\n            if (true) {\n                // Small delay to ensure Zustand has hydrated\n                setTimeout(()=>{\n                    setIsHydrated(true);\n                }, 100);\n            }\n        };\n        checkHydration();\n    }, []);\n    return {\n        ...store,\n        isHydrated,\n        // Helper to check if user is truly authenticated (after hydration)\n        isReady: isHydrated && !store.isLoading,\n        isLoggedIn: isHydrated && store.isAuthenticated && !!store.token\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/use-auth.ts\n"));

/***/ })

});