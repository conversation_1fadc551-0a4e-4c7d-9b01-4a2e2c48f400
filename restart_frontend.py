#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to restart the frontend development server.
"""

import os
import sys
import subprocess
import signal
import time
from pathlib import Path

def kill_process_on_port(port):
    """Kill any process running on the specified port."""
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(f'netstat -ano | findstr :{port}', shell=True, capture_output=True, text=True)
            if result.stdout:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if f':{port}' in line and 'LISTENING' in line:
                        parts = line.split()
                        if len(parts) > 4:
                            pid = parts[-1]
                            subprocess.run(f'taskkill /F /PID {pid}', shell=True)
                            print(f"✅ Killed process on port {port} (PID: {pid})")
        else:  # Unix/Linux/Mac
            subprocess.run(f'lsof -ti:{port} | xargs kill -9', shell=True)
            print(f"✅ Killed process on port {port}")
    except Exception as e:
        print(f"⚠️  Could not kill process on port {port}: {e}")

def main():
    """Main function."""
    print("🔄 Restarting Codora Frontend...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ Frontend directory not found")
        return False
    
    # Kill any process on port 3000
    print("🔍 Checking for processes on port 3000...")
    kill_process_on_port(3000)
    
    # Wait a moment
    time.sleep(2)
    
    # Change to frontend directory
    os.chdir(frontend_dir)
    
    # Start the development server
    print("🚀 Starting frontend development server...")
    try:
        subprocess.run("npm run dev", shell=True, check=True)
    except KeyboardInterrupt:
        print("\n⏹️  Development server stopped")
    except Exception as e:
        print(f"❌ Error starting development server: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
