"""
Database-backed Codora AI Agent using LangGraph.
"""

from typing import Dict, Any, List, AsyncGenerator
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage, ToolMessage
from langchain.chat_models import init_chat_model
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver
from sqlalchemy.orm import Session
from ..agent.database_tools import create_database_tools
from ..agent.prompt import SYSTEM_PROMPT
from .. import crud, schemas, models
from ..config import settings
import traceback


# Updated state for database-backed agent
from typing_extensions import TypedDict
from typing import List, Optional, Any

class DatabaseAgentState(TypedDict):
    """State for the database-backed agent."""
    messages: List[Any]
    project_id: Optional[int]
    version_id: Optional[int]
    tool_results: List[Any]
    waiting_for_user: bool
    last_error: Optional[str]


class DatabaseCodora:
    """Database-backed AI Web Development Assistant using LangGraph."""

    def __init__(self, model_name: str = None, model_provider: str = None):
        """Initialize the agent with specified model."""
        self.model_name = model_name or settings.model_name
        self.model_provider = model_provider or settings.model_provider
        self.api_key = settings.openai_api_key
        self.base_url = settings.openai_base_url
        self.llm = init_chat_model(self.model_name, model_provider=self.model_provider, api_key=self.api_key, base_url=self.base_url)

    def _create_graph(self, db: Session, version_id: int) -> StateGraph:
        """Create the LangGraph workflow for a specific version."""
        # Store db session for use in nodes
        self.db = db
        self.version_id = version_id

        # Create database tools for this version
        tools, fs_tools = create_database_tools(db, version_id)
        self.fs_tools = fs_tools

        # Bind tools to model
        llm_with_tools = self.llm.bind_tools(tools)

        # Create workflow
        workflow = StateGraph(DatabaseAgentState)

        def agent_node(state: DatabaseAgentState) -> DatabaseAgentState:
            """Main agent reasoning node."""
            system_prompt = self._get_system_prompt()
            messages = [SystemMessage(content=system_prompt)] + state["messages"]

            # Ensure all messages have content
            for message in messages:
                if not message.content:
                    message.content = "Response:"

            try:
                response = llm_with_tools.invoke(messages)
            except Exception as e:
                print(f"Agent error: {e}")
                response = AIMessage(content="I apologize, but I encountered an error. Let me try to help you without using tools for now.")

            return {
                **state,
                "messages": state["messages"] + [response],
                "waiting_for_user": False
            }

        def tools_node(state: DatabaseAgentState) -> DatabaseAgentState:
            """Tools execution node."""
            result = []
            messages = state["messages"]
            last_message = state["messages"][-1]
            tool_calls = last_message.tool_calls if hasattr(last_message, 'tool_calls') else []

            tools_by_name = {tool.name: tool for tool in tools}

            for tool_call in tool_calls:
                try:
                    tool = tools_by_name[tool_call["name"]]
                    observation = tool.invoke(tool_call["args"])
                    result.append(ToolMessage(
                        content=str(observation),
                        tool_call_id=tool_call["id"],
                        name=tool.name
                    ))
                except Exception as e:
                    result.append(ToolMessage(
                        content=f"Error executing tool {tool_call['name']}: {str(e)}",
                        tool_call_id=tool_call["id"],
                        name=tool_call["name"]
                    ))

            return {
                **state,
                "messages": messages + result
            }

        def should_continue(state: DatabaseAgentState) -> str:
            """Determine whether to continue with tools or end."""
            last_message = state["messages"][-1]
            if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                return "continue"
            return "end"

        # Add nodes
        workflow.add_node("agent", agent_node)
        workflow.add_node("tools", tools_node)

        # Add edges
        workflow.add_edge(START, "agent")
        workflow.add_conditional_edges(
            "agent",
            should_continue,
            {
                "continue": "tools",
                "end": END,
            }
        )
        workflow.add_edge("tools", "agent")

        # Compile with memory
        memory = MemorySaver()
        return workflow.compile(checkpointer=memory)

    def _get_system_prompt(self) -> str:
        """Get the system prompt for the agent."""
        return SYSTEM_PROMPT

    async def process_message_stream(
        self,
        user_input: str,
        project_id: int,
        db: Session,
        parent_message_id: int = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Process a user message and stream the response."""
        try:
            # Store db session for use throughout the method
            self.db = db

            # Create new version for this conversation first
            version = crud.create_project_version(db, project_id, None, f"User message: {user_input[:50]}...")

            # Create user message in database
            user_message = crud.create_message(db, schemas.MessageCreate(
                content=user_input,
                role="user",
                project_id=project_id,
                version_id=version.id,
                parent_message_id=parent_message_id
            ))

            # Update version with trigger message
            version.trigger_message_id = user_message.id
            db.commit()

            # Copy files from previous version if exists
            previous_version = crud.get_latest_version(db, project_id)
            if previous_version and previous_version.id != version.id:
                # Get previous version (second latest)
                versions = crud.get_project_versions(db, project_id)
                if len(versions) > 1:
                    prev_version = versions[-2]  # Second latest
                    prev_files = crud.get_version_files(db, prev_version.id)

                    for file in prev_files:
                        file_data = schemas.FileBase(
                            path=file.path,
                            content=file.content,
                            file_type=file.file_type
                        )
                        crud.create_file(db, file_data, version.id)

            # Create graph for this version
            graph = self._create_graph(db, version.id)

            # Initial state (without db_session to avoid serialization issues)
            initial_state: DatabaseAgentState = {
                "messages": [HumanMessage(content=user_input)],
                "project_id": project_id,
                "version_id": version.id,
                "tool_results": [],
                "waiting_for_user": False,
                "last_error": None
            }

            config = {"configurable": {"thread_id": f"project_{project_id}_version_{version.id}"}}

            # Stream the agent's response
            assistant_messages = []
            tool_executions = []

            for state in graph.stream(initial_state, config, stream_mode="values"):
                # Check if state has messages and is not empty
                if not state or "messages" not in state or not state["messages"]:
                    continue

                last_message = state["messages"][-1]

                if isinstance(last_message, AIMessage):
                    assistant_messages.append(last_message)
                    yield {
                        "type": "message",
                        "content": last_message.content or "",
                        "tool_calls": getattr(last_message, 'tool_calls', [])
                    }
                elif isinstance(last_message, ToolMessage):
                    tool_executions.append({
                        "tool_name": last_message.name,
                        "result": last_message.content
                    })
                    yield {
                        "type": "tool_result",
                        "tool_name": last_message.name,
                        "result": last_message.content
                    }

            # Save assistant message to database
            if assistant_messages:
                final_message = assistant_messages[-1]
                assistant_db_message = crud.create_message(db, schemas.MessageCreate(
                    content=final_message.content,
                    role="assistant",
                    project_id=project_id,
                    version_id=version.id,
                    parent_message_id=user_message.id
                ))

                # Save tool executions
                for tool_exec in tool_executions:
                    crud.create_tool_execution(db, schemas.ToolExecutionBase(
                        tool_name=tool_exec["tool_name"],
                        args={},
                        result=tool_exec["result"]
                    ), assistant_db_message.id)

            # Get created files
            try:
                files = self.fs_tools.get_all_files() if hasattr(self, 'fs_tools') else []
                yield {
                    "type": "completion",
                    "version_id": version.id,
                    "files_count": len(files)
                }
            except Exception as e:
                yield {
                    "type": "completion",
                    "version_id": version.id,
                    "files_count": 0,
                    "note": f"Could not count files: {str(e)}"
                }

        except Exception as e:
            error_details = traceback.format_exc()
            yield {
                "type": "error",
                "error": str(e),
                "details": error_details
            }
