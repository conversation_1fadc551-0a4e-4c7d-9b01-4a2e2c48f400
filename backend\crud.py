"""
CRUD operations for database models.
"""

from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import desc
from . import models, schemas
from .auth import get_password_hash


# User CRUD
def create_user(db: Session, user: schemas.UserCreate) -> models.User:
    """Create a new user."""
    hashed_password = get_password_hash(user.password)
    db_user = models.User(
        full_name=user.full_name,
        username=user.username,
        email=user.email,
        password_hash=hashed_password
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user


def get_user(db: Session, user_id: int) -> Optional[models.User]:
    """Get user by ID."""
    return db.query(models.User).filter(models.User.id == user_id).first()


def get_user_by_username(db: Session, username: str) -> Optional[models.User]:
    """Get user by username."""
    return db.query(models.User).filter(models.User.username == username).first()


def get_user_by_email(db: Session, email: str) -> Optional[models.User]:
    """Get user by email."""
    return db.query(models.User).filter(models.User.email == email).first()


# Project CRUD
def create_project(db: Session, project: schemas.ProjectCreate, user_id: int) -> models.Project:
    """Create a new project."""
    db_project = models.Project(**project.model_dump(), user_id=user_id)
    db.add(db_project)
    db.commit()
    db.refresh(db_project)
    return db_project


def get_project(db: Session, project_id: int) -> Optional[models.Project]:
    """Get project by ID."""
    return db.query(models.Project).filter(models.Project.id == project_id).first()


def get_project_by_share_token(db: Session, share_token: str) -> Optional[models.Project]:
    """Get project by share token."""
    return db.query(models.Project).filter(models.Project.share_token == share_token).first()


def get_user_projects(db: Session, user_id: int, skip: int = 0, limit: int = 100) -> List[models.Project]:
    """Get projects for a user."""
    return db.query(models.Project).filter(
        models.Project.user_id == user_id
    ).order_by(desc(models.Project.updated_at)).offset(skip).limit(limit).all()


def update_project(db: Session, project_id: int, project_update: schemas.ProjectUpdate) -> Optional[models.Project]:
    """Update a project."""
    db_project = get_project(db, project_id)
    if db_project:
        update_data = project_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_project, field, value)
        db.commit()
        db.refresh(db_project)
    return db_project


def delete_project(db: Session, project_id: int) -> bool:
    """Delete a project."""
    db_project = get_project(db, project_id)
    if db_project:
        db.delete(db_project)
        db.commit()
        return True
    return False


# Message CRUD
def create_message(db: Session, message: schemas.MessageCreate) -> models.Message:
    """Create a new message."""
    db_message = models.Message(**message.model_dump())
    db.add(db_message)
    db.commit()
    db.refresh(db_message)
    return db_message


def get_project_messages(db: Session, project_id: int) -> List[models.Message]:
    """Get all messages for a project."""
    return db.query(models.Message).filter(
        models.Message.project_id == project_id
    ).order_by(models.Message.created_at).all()


def get_message(db: Session, message_id: int) -> Optional[models.Message]:
    """Get message by ID."""
    return db.query(models.Message).filter(models.Message.id == message_id).first()


# Version CRUD
def create_project_version(db: Session, project_id: int, trigger_message_id: Optional[int] = None, description: Optional[str] = None) -> models.ProjectVersion:
    """Create a new project version."""
    # Get the next version number
    last_version = db.query(models.ProjectVersion).filter(
        models.ProjectVersion.project_id == project_id
    ).order_by(desc(models.ProjectVersion.version_number)).first()

    version_number = 1 if not last_version else last_version.version_number + 1

    db_version = models.ProjectVersion(
        project_id=project_id,
        version_number=version_number,
        trigger_message_id=trigger_message_id,
        description=description
    )
    db.add(db_version)
    db.commit()
    db.refresh(db_version)
    return db_version


def get_project_versions(db: Session, project_id: int) -> List[models.ProjectVersion]:
    """Get all versions for a project."""
    return db.query(models.ProjectVersion).filter(
        models.ProjectVersion.project_id == project_id
    ).order_by(models.ProjectVersion.version_number).all()


def get_latest_version(db: Session, project_id: int) -> Optional[models.ProjectVersion]:
    """Get the latest version for a project."""
    return db.query(models.ProjectVersion).filter(
        models.ProjectVersion.project_id == project_id
    ).order_by(desc(models.ProjectVersion.version_number)).first()


# File CRUD
def create_file(db: Session, file: schemas.FileBase, version_id: int) -> models.File:
    """Create a new file."""
    db_file = models.File(**file.model_dump(), version_id=version_id)
    db.add(db_file)
    db.commit()
    db.refresh(db_file)
    return db_file


def get_version_files(db: Session, version_id: int) -> List[models.File]:
    """Get all files for a version."""
    return db.query(models.File).filter(models.File.version_id == version_id).all()


# Tool execution CRUD
def create_tool_execution(db: Session, tool_execution: schemas.ToolExecutionBase, message_id: int) -> models.ToolExecution:
    """Create a new tool execution."""
    db_tool_execution = models.ToolExecution(**tool_execution.model_dump(), message_id=message_id)
    db.add(db_tool_execution)
    db.commit()
    db.refresh(db_tool_execution)
    return db_tool_execution
