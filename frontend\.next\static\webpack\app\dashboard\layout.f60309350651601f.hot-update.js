"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/auth/protected-route.tsx":
/*!*************************************************!*\
  !*** ./src/components/auth/protected-route.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProtectedRoute: function() { return /* binding */ ProtectedRoute; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* __next_internal_client_entry_do_not_use__ ProtectedRoute,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nfunction ProtectedRoute(param) {\n    let { children, fallback } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isAuthenticated, token, getCurrentUser, isLoading } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasHydrated, setHasHydrated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Wait for Zustand to hydrate from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            setHasHydrated(true);\n        }, 100) // Small delay to ensure hydration\n        ;\n        return ()=>clearTimeout(timer);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!hasHydrated) return; // Don't check auth until hydrated\n        const checkAuth = async ()=>{\n            console.log(\"Protected route check:\", {\n                token: !!token,\n                isAuthenticated,\n                hasHydrated\n            }) // Debug log\n            ;\n            if (token && isAuthenticated) {\n                // We have token and user data, all good\n                console.log(\"Auth check passed - user authenticated\") // Debug log\n                ;\n                setIsChecking(false);\n                return;\n            }\n            if (token && !isAuthenticated) {\n                // We have a token but no user data, try to get current user\n                console.log(\"Token found but not authenticated, getting current user\") // Debug log\n                ;\n                try {\n                    await getCurrentUser();\n                    setIsChecking(false);\n                } catch (error) {\n                    console.error(\"Failed to get current user:\", error) // Debug log\n                    ;\n                    // If getting user fails, redirect to login\n                    router.push(\"/auth/login\");\n                    return;\n                }\n            } else if (!token) {\n                // No token, redirect to login\n                console.log(\"No token found, redirecting to login\") // Debug log\n                ;\n                router.push(\"/auth/login\");\n                return;\n            }\n        };\n        checkAuth();\n    }, [\n        token,\n        isAuthenticated,\n        getCurrentUser,\n        router,\n        hasHydrated\n    ]);\n    // Show loading while checking authentication\n    if (isChecking || isLoading) {\n        return fallback || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-background\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin text-primary mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\auth\\\\protected-route.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\auth\\\\protected-route.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\auth\\\\protected-route.tsx\",\n                lineNumber: 69,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\auth\\\\protected-route.tsx\",\n            lineNumber: 68,\n            columnNumber: 9\n        }, this);\n    }\n    // If not authenticated after checking, don't render children\n    if (!isAuthenticated) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(ProtectedRoute, \"J6StOsAHfZyAFIZW06teyGxANd8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthStore\n    ];\n});\n_c = ProtectedRoute;\n// Hook for checking authentication status\nfunction useAuth() {\n    _s1();\n    const { isAuthenticated, user, token, logout } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    return {\n        isAuthenticated,\n        user,\n        token,\n        logout\n    };\n}\n_s1(useAuth, \"xsSez+ABDPF7GT2T8hWUnEQKcnQ=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthStore\n    ];\n});\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2F1dGgvcHJvdGVjdGVkLXJvdXRlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUUyQztBQUNBO0FBQ0E7QUFDTDtBQU8vQixTQUFTSyxlQUFlLEtBQTJDO1FBQTNDLEVBQUVDLFFBQVEsRUFBRUMsUUFBUSxFQUF1QixHQUEzQzs7SUFDN0IsTUFBTUMsU0FBU04sMERBQVNBO0lBQ3hCLE1BQU0sRUFBRU8sZUFBZSxFQUFFQyxLQUFLLEVBQUVDLGNBQWMsRUFBRUMsU0FBUyxFQUFFLEdBQUdULHlEQUFZQTtJQUMxRSxNQUFNLENBQUNVLFlBQVlDLGNBQWMsR0FBR2IsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDYyxhQUFhQyxlQUFlLEdBQUdmLCtDQUFRQSxDQUFDO0lBRS9DLGdEQUFnRDtJQUNoREQsZ0RBQVNBLENBQUM7UUFDUixNQUFNaUIsUUFBUUMsV0FBVztZQUN2QkYsZUFBZTtRQUNqQixHQUFHLEtBQUssa0NBQWtDOztRQUUxQyxPQUFPLElBQU1HLGFBQWFGO0lBQzVCLEdBQUcsRUFBRTtJQUVMakIsZ0RBQVNBLENBQUM7UUFDUixJQUFJLENBQUNlLGFBQWEsUUFBTyxrQ0FBa0M7UUFFM0QsTUFBTUssWUFBWTtZQUNoQkMsUUFBUUMsR0FBRyxDQUFDLDBCQUEwQjtnQkFBRVosT0FBTyxDQUFDLENBQUNBO2dCQUFPRDtnQkFBaUJNO1lBQVksR0FBRyxZQUFZOztZQUVwRyxJQUFJTCxTQUFTRCxpQkFBaUI7Z0JBQzVCLHdDQUF3QztnQkFDeENZLFFBQVFDLEdBQUcsQ0FBQywwQ0FBMEMsWUFBWTs7Z0JBQ2xFUixjQUFjO2dCQUNkO1lBQ0Y7WUFFQSxJQUFJSixTQUFTLENBQUNELGlCQUFpQjtnQkFDN0IsNERBQTREO2dCQUM1RFksUUFBUUMsR0FBRyxDQUFDLDJEQUEyRCxZQUFZOztnQkFDbkYsSUFBSTtvQkFDRixNQUFNWDtvQkFDTkcsY0FBYztnQkFDaEIsRUFBRSxPQUFPUyxPQUFPO29CQUNkRixRQUFRRSxLQUFLLENBQUMsK0JBQStCQSxPQUFPLFlBQVk7O29CQUNoRSwyQ0FBMkM7b0JBQzNDZixPQUFPZ0IsSUFBSSxDQUFDO29CQUNaO2dCQUNGO1lBQ0YsT0FBTyxJQUFJLENBQUNkLE9BQU87Z0JBQ2pCLDhCQUE4QjtnQkFDOUJXLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBd0MsWUFBWTs7Z0JBQ2hFZCxPQUFPZ0IsSUFBSSxDQUFDO2dCQUNaO1lBQ0Y7UUFDRjtRQUVBSjtJQUNGLEdBQUc7UUFBQ1Y7UUFBT0Q7UUFBaUJFO1FBQWdCSDtRQUFRTztLQUFZO0lBRWhFLDZDQUE2QztJQUM3QyxJQUFJRixjQUFjRCxXQUFXO1FBQzNCLE9BQ0VMLDBCQUNFLDhEQUFDa0I7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDdEIsbUZBQU9BO3dCQUFDc0IsV0FBVTs7Ozs7O2tDQUNuQiw4REFBQ0M7d0JBQUVELFdBQVU7a0NBQXdCOzs7Ozs7Ozs7Ozs7Ozs7OztJQUsvQztJQUVBLDZEQUE2RDtJQUM3RCxJQUFJLENBQUNqQixpQkFBaUI7UUFDcEIsT0FBTztJQUNUO0lBRUEscUJBQU87a0JBQUdIOztBQUNaO0dBdkVnQkQ7O1FBQ0NILHNEQUFTQTtRQUNzQ0MscURBQVlBOzs7S0FGNURFO0FBeUVoQiwwQ0FBMEM7QUFDbkMsU0FBU3VCOztJQUNkLE1BQU0sRUFBRW5CLGVBQWUsRUFBRW9CLElBQUksRUFBRW5CLEtBQUssRUFBRW9CLE1BQU0sRUFBRSxHQUFHM0IseURBQVlBO0lBRTdELE9BQU87UUFDTE07UUFDQW9CO1FBQ0FuQjtRQUNBb0I7SUFDRjtBQUNGO0lBVGdCRjs7UUFDbUN6QixxREFBWUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvYXV0aC9wcm90ZWN0ZWQtcm91dGUudHN4P2ZiNjUiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcbmltcG9ydCB7IHVzZUF1dGhTdG9yZSB9IGZyb20gJ0Avc3RvcmUvYXV0aCdcbmltcG9ydCB7IExvYWRlcjIgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5cbmludGVyZmFjZSBQcm90ZWN0ZWRSb3V0ZVByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxuICBmYWxsYmFjaz86IFJlYWN0LlJlYWN0Tm9kZVxufVxuXG5leHBvcnQgZnVuY3Rpb24gUHJvdGVjdGVkUm91dGUoeyBjaGlsZHJlbiwgZmFsbGJhY2sgfTogUHJvdGVjdGVkUm91dGVQcm9wcykge1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxuICBjb25zdCB7IGlzQXV0aGVudGljYXRlZCwgdG9rZW4sIGdldEN1cnJlbnRVc2VyLCBpc0xvYWRpbmcgfSA9IHVzZUF1dGhTdG9yZSgpXG4gIGNvbnN0IFtpc0NoZWNraW5nLCBzZXRJc0NoZWNraW5nXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFtoYXNIeWRyYXRlZCwgc2V0SGFzSHlkcmF0ZWRdID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgLy8gV2FpdCBmb3IgWnVzdGFuZCB0byBoeWRyYXRlIGZyb20gbG9jYWxTdG9yYWdlXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgdGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIHNldEhhc0h5ZHJhdGVkKHRydWUpXG4gICAgfSwgMTAwKSAvLyBTbWFsbCBkZWxheSB0byBlbnN1cmUgaHlkcmF0aW9uXG5cbiAgICByZXR1cm4gKCkgPT4gY2xlYXJUaW1lb3V0KHRpbWVyKVxuICB9LCBbXSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghaGFzSHlkcmF0ZWQpIHJldHVybiAvLyBEb24ndCBjaGVjayBhdXRoIHVudGlsIGh5ZHJhdGVkXG5cbiAgICBjb25zdCBjaGVja0F1dGggPSBhc3luYyAoKSA9PiB7XG4gICAgICBjb25zb2xlLmxvZygnUHJvdGVjdGVkIHJvdXRlIGNoZWNrOicsIHsgdG9rZW46ICEhdG9rZW4sIGlzQXV0aGVudGljYXRlZCwgaGFzSHlkcmF0ZWQgfSkgLy8gRGVidWcgbG9nXG5cbiAgICAgIGlmICh0b2tlbiAmJiBpc0F1dGhlbnRpY2F0ZWQpIHtcbiAgICAgICAgLy8gV2UgaGF2ZSB0b2tlbiBhbmQgdXNlciBkYXRhLCBhbGwgZ29vZFxuICAgICAgICBjb25zb2xlLmxvZygnQXV0aCBjaGVjayBwYXNzZWQgLSB1c2VyIGF1dGhlbnRpY2F0ZWQnKSAvLyBEZWJ1ZyBsb2dcbiAgICAgICAgc2V0SXNDaGVja2luZyhmYWxzZSlcbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG5cbiAgICAgIGlmICh0b2tlbiAmJiAhaXNBdXRoZW50aWNhdGVkKSB7XG4gICAgICAgIC8vIFdlIGhhdmUgYSB0b2tlbiBidXQgbm8gdXNlciBkYXRhLCB0cnkgdG8gZ2V0IGN1cnJlbnQgdXNlclxuICAgICAgICBjb25zb2xlLmxvZygnVG9rZW4gZm91bmQgYnV0IG5vdCBhdXRoZW50aWNhdGVkLCBnZXR0aW5nIGN1cnJlbnQgdXNlcicpIC8vIERlYnVnIGxvZ1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGF3YWl0IGdldEN1cnJlbnRVc2VyKClcbiAgICAgICAgICBzZXRJc0NoZWNraW5nKGZhbHNlKVxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBnZXQgY3VycmVudCB1c2VyOicsIGVycm9yKSAvLyBEZWJ1ZyBsb2dcbiAgICAgICAgICAvLyBJZiBnZXR0aW5nIHVzZXIgZmFpbHMsIHJlZGlyZWN0IHRvIGxvZ2luXG4gICAgICAgICAgcm91dGVyLnB1c2goJy9hdXRoL2xvZ2luJylcbiAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgfSBlbHNlIGlmICghdG9rZW4pIHtcbiAgICAgICAgLy8gTm8gdG9rZW4sIHJlZGlyZWN0IHRvIGxvZ2luXG4gICAgICAgIGNvbnNvbGUubG9nKCdObyB0b2tlbiBmb3VuZCwgcmVkaXJlY3RpbmcgdG8gbG9naW4nKSAvLyBEZWJ1ZyBsb2dcbiAgICAgICAgcm91dGVyLnB1c2goJy9hdXRoL2xvZ2luJylcbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG4gICAgfVxuXG4gICAgY2hlY2tBdXRoKClcbiAgfSwgW3Rva2VuLCBpc0F1dGhlbnRpY2F0ZWQsIGdldEN1cnJlbnRVc2VyLCByb3V0ZXIsIGhhc0h5ZHJhdGVkXSlcblxuICAvLyBTaG93IGxvYWRpbmcgd2hpbGUgY2hlY2tpbmcgYXV0aGVudGljYXRpb25cbiAgaWYgKGlzQ2hlY2tpbmcgfHwgaXNMb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIGZhbGxiYWNrIHx8IChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYmctYmFja2dyb3VuZFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cImgtOCB3LTggYW5pbWF0ZS1zcGluIHRleHQtcHJpbWFyeSBteC1hdXRvIG1iLTRcIiAvPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+TG9hZGluZy4uLjwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApXG4gICAgKVxuICB9XG5cbiAgLy8gSWYgbm90IGF1dGhlbnRpY2F0ZWQgYWZ0ZXIgY2hlY2tpbmcsIGRvbid0IHJlbmRlciBjaGlsZHJlblxuICBpZiAoIWlzQXV0aGVudGljYXRlZCkge1xuICAgIHJldHVybiBudWxsXG4gIH1cblxuICByZXR1cm4gPD57Y2hpbGRyZW59PC8+XG59XG5cbi8vIEhvb2sgZm9yIGNoZWNraW5nIGF1dGhlbnRpY2F0aW9uIHN0YXR1c1xuZXhwb3J0IGZ1bmN0aW9uIHVzZUF1dGgoKSB7XG4gIGNvbnN0IHsgaXNBdXRoZW50aWNhdGVkLCB1c2VyLCB0b2tlbiwgbG9nb3V0IH0gPSB1c2VBdXRoU3RvcmUoKVxuXG4gIHJldHVybiB7XG4gICAgaXNBdXRoZW50aWNhdGVkLFxuICAgIHVzZXIsXG4gICAgdG9rZW4sXG4gICAgbG9nb3V0LFxuICB9XG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VSb3V0ZXIiLCJ1c2VBdXRoU3RvcmUiLCJMb2FkZXIyIiwiUHJvdGVjdGVkUm91dGUiLCJjaGlsZHJlbiIsImZhbGxiYWNrIiwicm91dGVyIiwiaXNBdXRoZW50aWNhdGVkIiwidG9rZW4iLCJnZXRDdXJyZW50VXNlciIsImlzTG9hZGluZyIsImlzQ2hlY2tpbmciLCJzZXRJc0NoZWNraW5nIiwiaGFzSHlkcmF0ZWQiLCJzZXRIYXNIeWRyYXRlZCIsInRpbWVyIiwic2V0VGltZW91dCIsImNsZWFyVGltZW91dCIsImNoZWNrQXV0aCIsImNvbnNvbGUiLCJsb2ciLCJlcnJvciIsInB1c2giLCJkaXYiLCJjbGFzc05hbWUiLCJwIiwidXNlQXV0aCIsInVzZXIiLCJsb2dvdXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/protected-route.tsx\n"));

/***/ })

});