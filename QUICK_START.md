# 🚀 Codora Quick Start Guide

## Prerequisites

1. **Python 3.8+** installed
2. **PostgreSQL 12+** installed and running
3. **OpenAI API Key** (or other LLM provider)

## Step 1: Database Setup

### Option A: Local PostgreSQL
```bash
# Create database
createdb codora

# Or using psql
psql -c "CREATE DATABASE codora;"
```

### Option B: Docker PostgreSQL (Optional)
```bash
docker-compose -f docker-compose.dev.yml up -d
```

## Step 2: Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your settings
# Required: DATABASE_URL, SECRET_KEY, OPENAI_API_KEY
```

Example `.env`:
```env
DATABASE_URL=postgresql://username:password@localhost:5432/codora
SECRET_KEY=your-super-secret-key-change-this
OPENAI_API_KEY=sk-your-openai-key-here
MODEL_NAME=gpt-4o-mini
MODEL_PROVIDER=openai
DEBUG=True
```

## Step 3: Install & Setup

```bash
# Install dependencies and setup database
python setup_database.py
```

This will:
- ✅ Install all Python packages
- ✅ Create database tables

## Step 4: Run Backend

```bash
# Start the backend server
python run_backend.py
```

Server will be available at:
- 🌐 **API**: http://localhost:8000
- 📚 **Docs**: http://localhost:8000/docs
- 📖 **ReDoc**: http://localhost:8000/redoc

## Step 5: Test Everything

```bash
# Run basic tests
python test_backend.py
```

## 🎉 You're Ready!

The backend is now running and ready for the frontend!

### What's Next?

1. **Frontend Development**: Create the Next.js frontend
2. **Test the API**: Use the interactive docs at `/docs`
3. **Create a User**: Register via `/auth/register`
4. **Create Projects**: Start building with the AI agent

### API Quick Test

```bash
# Register a user
curl -X POST "http://localhost:8000/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "full_name": "John Doe",
    "username": "johndoe",
    "email": "<EMAIL>",
    "password": "password123"
  }'

# Login
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username_or_email": "johndoe",
    "password": "password123"
  }'
```

### Troubleshooting

**Database Connection Issues:**
- Check PostgreSQL is running: `pg_isready`
- Verify DATABASE_URL in `.env`
- Check database exists: `psql -l`

**Import Errors:**
- Ensure virtual environment is activated
- Run `pip install -r requirements.txt`

**API Key Issues:**
- Verify OPENAI_API_KEY in `.env`
- Test key: `curl -H "Authorization: Bearer YOUR_KEY" https://api.openai.com/v1/models`

### Development Commands

```bash
# Recreate database tables
python create_manual_migration.py

# Run with different port
uvicorn backend.main:app --port 8001 --reload

# Run tests
python test_backend.py
```

### Project Structure

```
codora/
├── backend/              # FastAPI backend
│   ├── main.py          # Main application
│   ├── models.py        # Database models
│   ├── routers/         # API routes
│   └── agent/           # AI agent code

├── requirements.txt     # Python dependencies
├── .env                 # Environment variables
└── setup_database.py   # Setup script
```

Happy coding! 🎨✨
