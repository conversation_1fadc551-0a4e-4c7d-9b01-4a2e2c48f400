# Codora Backend

AI Web Development Assistant Backend built with FastAPI, PostgreSQL, and LangGraph.

## Features

- 🔐 **User Authentication** - JWT-based auth with email/username login
- 🗂️ **Project Management** - Multi-user project support with versioning
- 🤖 **AI Agent Integration** - LangGraph-powered autonomous web development
- 📡 **Real-time Streaming** - Server-Sent Events for live agent responses
- 🔄 **Version Control** - Time travel and project versioning system
- 🔗 **Project Sharing** - Share projects with view-only and fork capabilities
- 📁 **File Management** - Database-backed file system for projects

## Tech Stack

- **FastAPI** - Modern Python web framework
- **PostgreSQL** - Primary database
- **SQLAlchemy** - ORM and database toolkit
- **Alembic** - Database migrations
- **LangGraph** - AI agent framework
- **JWT** - Authentication tokens
- **Server-Sent Events** - Real-time streaming

## Quick Start

### 1. Prerequisites

- Python 3.8+
- PostgreSQL 12+
- OpenAI API key (or other LLM provider)

### 2. Database Setup

Create a PostgreSQL database:
```sql
CREATE DATABASE codora;
```

### 3. Environment Configuration

Copy the example environment file:
```bash
cp .env.example .env
```

Edit `.env` with your configuration:
```env
DATABASE_URL=postgresql://username:password@localhost:5432/codora
SECRET_KEY=your-super-secret-key-here-change-this-in-production
OPENAI_API_KEY=your-openai-api-key-here
```

### 4. Install Dependencies & Setup Database

```bash
python setup_database.py
```

This will:
- Install all Python dependencies
- Initialize Alembic migrations
- Create database tables
- Set up the initial schema

### 5. Run the Server

```bash
python run_backend.py
```

The server will start at `http://localhost:8000`

## API Documentation

Once the server is running, visit:
- **Interactive API Docs**: http://localhost:8000/docs
- **ReDoc Documentation**: http://localhost:8000/redoc

## API Endpoints

### Authentication
- `POST /auth/register` - Register new user
- `POST /auth/login` - Login user
- `GET /auth/me` - Get current user info

### Projects
- `GET /projects/` - List user projects
- `POST /projects/` - Create new project
- `GET /projects/{id}` - Get project details
- `PUT /projects/{id}` - Update project
- `DELETE /projects/{id}` - Delete project

### Chat & AI Agent
- `POST /chat/{project_id}/message` - Send message to AI agent (streaming)
- `GET /chat/{project_id}/history` - Get chat history
- `GET /chat/{project_id}/versions/{version_id}/preview` - Preview project version

### Sharing
- `GET /projects/shared/{token}` - View shared project
- `POST /projects/fork/{token}` - Fork shared project

## Database Schema

### Core Tables
- **users** - User accounts and authentication
- **projects** - User projects with sharing capabilities
- **project_versions** - Version control for projects
- **messages** - Chat history and conversations
- **files** - Project files stored in database
- **tool_executions** - AI agent tool usage tracking

## Development

### Database Migrations

Create a new migration:
```bash
alembic revision --autogenerate -m "Description of changes"
```

Apply migrations:
```bash
alembic upgrade head
```

### Running Tests

```bash
pytest
```

### Code Structure

```
backend/
├── main.py              # FastAPI application
├── config.py            # Configuration settings
├── database.py          # Database connection
├── models.py            # SQLAlchemy models
├── schemas.py           # Pydantic schemas
├── auth.py              # Authentication utilities
├── crud.py              # Database operations
├── routers/             # API route handlers
│   ├── auth.py
│   ├── projects.py
│   └── chat.py
└── agent/               # AI agent integration
    ├── codora_agent.py  # Main agent class
    └── database_tools.py # Database-backed tools
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | Required |
| `SECRET_KEY` | JWT signing key | Required |
| `OPENAI_API_KEY` | OpenAI API key | Required |
| `MODEL_NAME` | AI model to use | `gpt-4o-mini` |
| `MODEL_PROVIDER` | AI provider | `openai` |
| `DEBUG` | Enable debug mode | `True` |
| `CORS_ORIGINS` | Allowed CORS origins | `["http://localhost:3000"]` |

## Security Features

- 🔐 JWT-based authentication
- 🛡️ Password hashing with bcrypt
- 🔒 CORS protection
- 🚫 SQL injection prevention via SQLAlchemy
- 👤 User-based access control
- 🔗 Secure project sharing with tokens

## Performance Considerations

- Database connection pooling
- Efficient file storage in PostgreSQL
- Streaming responses for real-time updates
- Optimized queries with SQLAlchemy
- Caching strategies for frequently accessed data

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check PostgreSQL is running
   - Verify DATABASE_URL in .env
   - Ensure database exists

2. **Migration Errors**
   - Check Alembic configuration
   - Verify database permissions
   - Run `alembic current` to check status

3. **AI Agent Errors**
   - Verify API keys are set
   - Check model availability
   - Review agent logs

### Logs

The application logs to stdout. For production, configure proper logging:

```python
import logging
logging.basicConfig(level=logging.INFO)
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
