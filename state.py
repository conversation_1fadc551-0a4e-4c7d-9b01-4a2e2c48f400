"""
State definition for the AI web development assistant agent.
"""

from typing import List, Dict, Any, Optional
from typing_extensions import TypedDict
from langchain_core.messages import BaseMessage


class AgentState(TypedDict):
    """State for the web development assistant agent."""
    
    # Conversation messages
    messages: List[BaseMessage]
    
    # Current task being worked on
    current_task: Optional[str]
    
    # Project context
    project_name: Optional[str]
    project_description: Optional[str]
    
    # File system context
    current_directory: str
    recent_files: List[str]
    
    # Agent reasoning
    thoughts: List[str]
    
    # Tool results
    tool_results: List[Dict[str, Any]]
    
    # User interaction flags
    waiting_for_user: bool
    
    # Error handling
    last_error: Optional[str]
    retry_count: int
