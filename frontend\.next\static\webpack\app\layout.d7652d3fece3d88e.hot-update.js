"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"fd41ec70340d\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/NDhlYiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImZkNDFlYzcwMzQwZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/theme-script.tsx":
/*!*****************************************!*\
  !*** ./src/components/theme-script.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeScript: function() { return /* binding */ ThemeScript; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ThemeScript auto */ \nfunction ThemeScript() {\n    const themeScript = \"\\n    (function() {\\n      try {\\n        // Apply theme\\n        var theme = localStorage.getItem('codora-ui-theme') || 'dark';\\n        var root = document.documentElement;\\n\\n        // Remove any existing theme classes\\n        root.classList.remove('light', 'dark');\\n\\n        // Apply the stored theme immediately before any rendering\\n        if (theme === 'system') {\\n          var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\\n          root.classList.add(systemTheme);\\n          root.style.colorScheme = systemTheme;\\n        } else {\\n          root.classList.add(theme);\\n          root.style.colorScheme = theme;\\n        }\\n\\n        // Check auth state and set global flag\\n        var authStorage = localStorage.getItem('auth-storage');\\n        var isAuthenticated = false;\\n\\n        if (authStorage) {\\n          try {\\n            var authData = JSON.parse(authStorage);\\n            isAuthenticated = !!(authData.state && authData.state.token && authData.state.isAuthenticated);\\n          } catch (e) {\\n            // Invalid auth data\\n            isAuthenticated = false;\\n          }\\n        }\\n\\n        // Set global flags for immediate access\\n        window.__INITIAL_AUTH_STATE__ = isAuthenticated;\\n        window.__THEME_READY__ = true;\\n\\n        // Set attributes for CSS targeting\\n        root.setAttribute('data-theme-ready', 'true');\\n        root.setAttribute('data-auth-state', isAuthenticated ? 'authenticated' : 'unauthenticated');\\n\\n      } catch (e) {\\n        // Fallback\\n        document.documentElement.classList.add('dark');\\n        document.documentElement.style.colorScheme = 'dark';\\n        document.documentElement.setAttribute('data-theme-ready', 'true');\\n        document.documentElement.setAttribute('data-auth-state', 'unauthenticated');\\n        window.__INITIAL_AUTH_STATE__ = false;\\n        window.__THEME_READY__ = true;\\n      }\\n    })();\\n  \";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n        dangerouslySetInnerHTML: {\n            __html: themeScript\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\theme-script.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n_c = ThemeScript;\nvar _c;\n$RefreshReg$(_c, \"ThemeScript\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/theme-script.tsx\n"));

/***/ })

});