// User types
export interface User {
  id: number
  full_name: string
  username: string
  email: string
  created_at: string
  updated_at: string
}

export interface AuthResponse {
  access_token: string
  token_type: string
  user: User
}

// Project types
export interface Project {
  id: number
  name: string
  description?: string
  user_id: number
  is_public: boolean
  share_token?: string
  created_at: string
  updated_at: string
  versions?: ProjectVersion[]
  messages?: Message[]
}

export interface ProjectCreate {
  name: string
  description?: string
}

export interface ProjectUpdate {
  name?: string
  description?: string
  is_public?: boolean
}

// Version types
export interface ProjectVersion {
  id: number
  project_id: number
  version_number: number
  trigger_message_id?: number
  description?: string
  created_at: string
  files: File[]
}

// Message types
export interface Message {
  id: number
  project_id: number
  version_id?: number
  content: string
  role: 'user' | 'assistant'
  parent_message_id?: number
  created_at: string
  tool_executions?: ToolExecution[]
}

export interface MessageCreate {
  content: string
  project_id: number
  version_id?: number
  parent_message_id?: number
}

// File types
export interface File {
  id: number
  version_id: number
  path: string
  content?: string
  file_type?: string
  created_at: string
}

export interface FileCreate {
  path: string
  content?: string
  file_type?: string
}

// Tool execution types
export interface ToolExecution {
  id: number
  message_id: number
  tool_name: string
  args: Record<string, any>
  result?: string
  created_at: string
}

// Chat streaming types
export interface ChatStreamMessage {
  type: 'message' | 'tool_result' | 'completion' | 'error' | 'done'
  content?: string
  tool_name?: string
  result?: string
  tool_calls?: any[]
  version_id?: number
  files_count?: number
  error?: string
  details?: string
}

// UI State types
export interface ChatState {
  messages: Message[]
  isLoading: boolean
  isStreaming: boolean
  currentVersion?: ProjectVersion
}

export interface ProjectState {
  currentProject?: Project
  projects: Project[]
  isLoading: boolean
  error?: string
}

export interface FileState {
  files: File[]
  selectedFile?: File
  isLoading: boolean
  error?: string
}

export interface VersionState {
  versions: ProjectVersion[]
  currentVersion?: ProjectVersion
  isLoading: boolean
  error?: string
}

// Form types
export interface LoginForm {
  username_or_email: string
  password: string
}

export interface RegisterForm {
  full_name: string
  username: string
  email: string
  password: string
  confirmPassword: string
}

export interface ProjectForm {
  name: string
  description: string
}

// API Response types
export interface ApiResponse<T = any> {
  data: T
  message?: string
  success: boolean
}

export interface ApiError {
  message: string
  details?: string
  status: number
}

// Component props types
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface ModalProps extends BaseComponentProps {
  isOpen: boolean
  onClose: () => void
  title?: string
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  disabled?: boolean
  loading?: boolean
  onClick?: () => void
  type?: 'button' | 'submit' | 'reset'
}

// Navigation types
export interface NavItem {
  title: string
  href: string
  icon?: React.ComponentType<{ className?: string }>
  disabled?: boolean
  external?: boolean
}

export interface SidebarItem extends NavItem {
  badge?: string | number
  children?: SidebarItem[]
}

// Theme types
export type Theme = 'light' | 'dark' | 'system'

// File tree types
export interface FileTreeNode {
  name: string
  path: string
  type: 'file' | 'folder'
  children?: FileTreeNode[]
  size?: number
  modified?: string
}

// Version tree types
export interface VersionTreeNode {
  version: ProjectVersion
  children: VersionTreeNode[]
  parent?: VersionTreeNode
  depth: number
}

// Search types
export interface SearchResult {
  type: 'project' | 'file' | 'message'
  id: number
  title: string
  description?: string
  path?: string
  highlight?: string
  project?: Project
}

// Settings types
export interface UserSettings {
  theme: Theme
  language: string
  notifications: {
    email: boolean
    push: boolean
    desktop: boolean
  }
  editor: {
    fontSize: number
    tabSize: number
    wordWrap: boolean
    minimap: boolean
  }
}
