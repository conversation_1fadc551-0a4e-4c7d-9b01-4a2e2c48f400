#!/usr/bin/env python3
"""
Simple test for the LangGraph agent without database.
"""

import os
from dotenv import load_dotenv
from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver
from typing import Dict, Any, List

# Load environment variables
load_dotenv()

class SimpleAgentState(dict):
    """Simple state for testing."""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.setdefault("messages", [])

def test_simple_agent():
    """Test a simple agent without database dependencies."""
    print("🧪 Testing Simple LangGraph Agent...")
    
    # Check if API key is available
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key or api_key == "your-openai-api-key-here":
        print("❌ OpenAI API key not found or not set in .env file")
        print("💡 Please set OPENAI_API_KEY in your .env file")
        return False
    
    try:
        # Initialize model
        print("🔄 Initializing AI model...")
        model_name = os.getenv("MODEL_NAME", "gpt-4o-mini")
        model_provider = os.getenv("MODEL_PROVIDER", "openai")
        
        llm = init_chat_model(model_name, model_provider=model_provider)
        print(f"✅ Model initialized: {model_name}")
        
        # Create simple workflow
        workflow = StateGraph(SimpleAgentState)
        
        def agent_node(state: SimpleAgentState) -> Dict[str, Any]:
            """Simple agent node."""
            system_prompt = "You are a helpful AI assistant. Respond briefly and clearly."
            messages = [SystemMessage(content=system_prompt)] + state["messages"]
            
            response = llm.invoke(messages)
            return {"messages": state["messages"] + [response]}
        
        # Add node and edges
        workflow.add_node("agent", agent_node)
        workflow.add_edge(START, "agent")
        workflow.add_edge("agent", END)
        
        # Compile with memory
        memory = MemorySaver()
        graph = workflow.compile(checkpointer=memory)
        
        print("✅ LangGraph workflow created successfully")
        
        # Test the agent
        print("🔄 Testing agent response...")
        initial_state = SimpleAgentState(
            messages=[HumanMessage(content="Hello! Can you tell me what 2+2 equals?")]
        )
        
        config = {"configurable": {"thread_id": "test_thread"}}
        
        # Run the agent
        result = graph.invoke(initial_state, config)
        
        if result and "messages" in result and len(result["messages"]) > 1:
            last_message = result["messages"][-1]
            if isinstance(last_message, AIMessage):
                print(f"✅ Agent responded: {last_message.content[:100]}...")
                return True
        
        print("❌ Agent did not respond as expected")
        return False
        
    except Exception as e:
        print(f"❌ Error testing simple agent: {e}")
        import traceback
        print(f"📋 Details: {traceback.format_exc()}")
        return False

def test_model_only():
    """Test just the model without LangGraph."""
    print("\n🧪 Testing AI Model Only...")
    
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key or api_key == "your-openai-api-key-here":
        print("❌ OpenAI API key not found")
        return False
    
    try:
        model_name = os.getenv("MODEL_NAME", "gpt-4o-mini")
        model_provider = os.getenv("MODEL_PROVIDER", "openai")
        
        llm = init_chat_model(model_name, model_provider=model_provider)
        
        # Simple test
        response = llm.invoke([HumanMessage(content="What is 2+2?")])
        
        if response and response.content:
            print(f"✅ Model responded: {response.content[:100]}...")
            return True
        else:
            print("❌ Model did not respond")
            return False
            
    except Exception as e:
        print(f"❌ Error testing model: {e}")
        return False

def main():
    """Run all simple tests."""
    print("🚀 Starting Simple Agent Tests...\n")
    
    # Test model only first
    if not test_model_only():
        print("\n❌ Basic model test failed")
        return False
    
    # Test simple agent
    if not test_simple_agent():
        print("\n❌ Simple agent test failed")
        return False
    
    print("\n🎉 All simple tests passed!")
    print("✅ AI model is working")
    print("✅ LangGraph is working")
    print("✅ Ready for database integration")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💡 Troubleshooting:")
        print("1. Check your .env file exists and has OPENAI_API_KEY")
        print("2. Verify your OpenAI API key is valid")
        print("3. Check your internet connection")
        print("4. Try a different model (e.g., gpt-3.5-turbo)")
    exit(0 if success else 1)
