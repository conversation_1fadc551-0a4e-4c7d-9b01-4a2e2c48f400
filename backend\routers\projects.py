"""
Project management routes.
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from .. import crud, schemas, models
from ..database import get_db
from ..auth import get_current_user

router = APIRouter(prefix="/projects", tags=["projects"])


@router.post("/", response_model=schemas.Project)
def create_project(
    project: schemas.ProjectCreate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """Create a new project."""
    return crud.create_project(db=db, project=project, user_id=current_user.id)


@router.get("/", response_model=List[schemas.Project])
def read_projects(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """Get current user's projects."""
    return crud.get_user_projects(db, user_id=current_user.id, skip=skip, limit=limit)


@router.get("/{project_id}", response_model=schemas.Project)
def read_project(
    project_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """Get a specific project."""
    project = crud.get_project(db, project_id=project_id)
    if project is None:
        raise HTTPException(status_code=404, detail="Project not found")
    
    # Check if user owns the project or if it's public
    if project.user_id != current_user.id and not project.is_public:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    return project


@router.put("/{project_id}", response_model=schemas.Project)
def update_project(
    project_id: int,
    project_update: schemas.ProjectUpdate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """Update a project."""
    project = crud.get_project(db, project_id=project_id)
    if project is None:
        raise HTTPException(status_code=404, detail="Project not found")
    
    # Check if user owns the project
    if project.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    return crud.update_project(db, project_id=project_id, project_update=project_update)


@router.delete("/{project_id}")
def delete_project(
    project_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """Delete a project."""
    project = crud.get_project(db, project_id=project_id)
    if project is None:
        raise HTTPException(status_code=404, detail="Project not found")
    
    # Check if user owns the project
    if project.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    success = crud.delete_project(db, project_id=project_id)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to delete project")
    
    return {"message": "Project deleted successfully"}


@router.get("/{project_id}/messages", response_model=List[schemas.Message])
def read_project_messages(
    project_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """Get all messages for a project."""
    project = crud.get_project(db, project_id=project_id)
    if project is None:
        raise HTTPException(status_code=404, detail="Project not found")
    
    # Check if user owns the project or if it's public
    if project.user_id != current_user.id and not project.is_public:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    return crud.get_project_messages(db, project_id=project_id)


@router.get("/{project_id}/versions", response_model=List[schemas.ProjectVersion])
def read_project_versions(
    project_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """Get all versions for a project."""
    project = crud.get_project(db, project_id=project_id)
    if project is None:
        raise HTTPException(status_code=404, detail="Project not found")
    
    # Check if user owns the project or if it's public
    if project.user_id != current_user.id and not project.is_public:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    return crud.get_project_versions(db, project_id=project_id)


@router.get("/{project_id}/versions/{version_id}/files", response_model=List[schemas.File])
def read_version_files(
    project_id: int,
    version_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """Get all files for a specific version."""
    project = crud.get_project(db, project_id=project_id)
    if project is None:
        raise HTTPException(status_code=404, detail="Project not found")
    
    # Check if user owns the project or if it's public
    if project.user_id != current_user.id and not project.is_public:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    return crud.get_version_files(db, version_id=version_id)


# Public routes for sharing
@router.get("/shared/{share_token}", response_model=schemas.Project)
def read_shared_project(share_token: str, db: Session = Depends(get_db)):
    """Get a project by share token (public access)."""
    project = crud.get_project_by_share_token(db, share_token=share_token)
    if project is None:
        raise HTTPException(status_code=404, detail="Project not found")
    
    return project


@router.get("/shared/{share_token}/messages", response_model=List[schemas.Message])
def read_shared_project_messages(share_token: str, db: Session = Depends(get_db)):
    """Get messages for a shared project."""
    project = crud.get_project_by_share_token(db, share_token=share_token)
    if project is None:
        raise HTTPException(status_code=404, detail="Project not found")
    
    return crud.get_project_messages(db, project_id=project.id)


@router.get("/shared/{share_token}/versions", response_model=List[schemas.ProjectVersion])
def read_shared_project_versions(share_token: str, db: Session = Depends(get_db)):
    """Get versions for a shared project."""
    project = crud.get_project_by_share_token(db, share_token=share_token)
    if project is None:
        raise HTTPException(status_code=404, detail="Project not found")
    
    return crud.get_project_versions(db, project_id=project.id)


@router.post("/fork/{share_token}", response_model=schemas.Project)
def fork_project(
    share_token: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """Fork a shared project."""
    original_project = crud.get_project_by_share_token(db, share_token=share_token)
    if original_project is None:
        raise HTTPException(status_code=404, detail="Project not found")
    
    # Create a new project as a fork
    fork_data = schemas.ProjectCreate(
        name=f"{original_project.name} (Fork)",
        description=f"Forked from {original_project.name}"
    )
    
    forked_project = crud.create_project(db=db, project=fork_data, user_id=current_user.id)
    
    # Copy the latest version and its files
    latest_version = crud.get_latest_version(db, original_project.id)
    if latest_version:
        new_version = crud.create_project_version(db, forked_project.id)
        files = crud.get_version_files(db, latest_version.id)
        
        for file in files:
            file_data = schemas.FileBase(
                path=file.path,
                content=file.content,
                file_type=file.file_type
            )
            crud.create_file(db, file_data, new_version.id)
    
    return forked_project
