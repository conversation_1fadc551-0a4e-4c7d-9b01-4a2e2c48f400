'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  GitBranch,
  Clock,
  User,
  MoreVertical,
  Eye,
  Download,
  GitCommit,
  Tag,
  ArrowRight,
  Plus,
  Restore,
  Copy,
} from 'lucide-react'

interface Version {
  id: string
  version: string
  title: string
  description: string
  created_at: string
  author: {
    name: string
    username: string
  }
  changes: {
    files_added: number
    files_modified: number
    files_deleted: number
  }
  is_current: boolean
  size: number
}

interface Project {
  id: number
  name: string
}

export default function VersionsPage() {
  const params = useParams()
  const [project, setProject] = useState<Project | null>(null)
  const [versions, setVersions] = useState<Version[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Mock project data
    setProject({
      id: parseInt(params.id as string),
      name: 'My Portfolio Website'
    })

    // Mock versions data
    const mockVersions: Version[] = [
      {
        id: '5',
        version: '1.4.0',
        title: 'Added contact form and animations',
        description: 'Implemented a fully functional contact form with validation and added smooth scroll animations throughout the site.',
        created_at: '2024-01-20T14:30:00Z',
        author: {
          name: 'John Doe',
          username: 'johndoe'
        },
        changes: {
          files_added: 2,
          files_modified: 3,
          files_deleted: 0
        },
        is_current: true,
        size: 15680
      },
      {
        id: '4',
        version: '1.3.0',
        title: 'Enhanced responsive design',
        description: 'Improved mobile responsiveness and added tablet-specific layouts. Fixed several CSS issues.',
        created_at: '2024-01-18T16:20:00Z',
        author: {
          name: 'John Doe',
          username: 'johndoe'
        },
        changes: {
          files_added: 1,
          files_modified: 4,
          files_deleted: 1
        },
        is_current: false,
        size: 14200
      },
      {
        id: '3',
        version: '1.2.0',
        title: 'Added projects section',
        description: 'Created a dynamic projects showcase with filtering capabilities and project detail modals.',
        created_at: '2024-01-15T11:45:00Z',
        author: {
          name: 'John Doe',
          username: 'johndoe'
        },
        changes: {
          files_added: 3,
          files_modified: 2,
          files_deleted: 0
        },
        is_current: false,
        size: 12800
      },
      {
        id: '2',
        version: '1.1.0',
        title: 'Improved navigation and styling',
        description: 'Enhanced the navigation menu with better UX and updated the overall color scheme.',
        created_at: '2024-01-12T09:30:00Z',
        author: {
          name: 'John Doe',
          username: 'johndoe'
        },
        changes: {
          files_added: 0,
          files_modified: 3,
          files_deleted: 0
        },
        is_current: false,
        size: 10240
      },
      {
        id: '1',
        version: '1.0.0',
        title: 'Initial portfolio structure',
        description: 'Created the basic structure with hero section, about me, and initial styling.',
        created_at: '2024-01-10T14:00:00Z',
        author: {
          name: 'John Doe',
          username: 'johndoe'
        },
        changes: {
          files_added: 3,
          files_modified: 0,
          files_deleted: 0
        },
        is_current: false,
        size: 8192
      }
    ]

    setTimeout(() => {
      setVersions(mockVersions)
      setIsLoading(false)
    }, 1000)
  }, [params.id])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getChangesSummary = (changes: Version['changes']) => {
    const parts = []
    if (changes.files_added > 0) parts.push(`+${changes.files_added}`)
    if (changes.files_modified > 0) parts.push(`~${changes.files_modified}`)
    if (changes.files_deleted > 0) parts.push(`-${changes.files_deleted}`)
    return parts.join(' ')
  }

  const handleRestoreVersion = (versionId: string) => {
    console.log('Restore version:', versionId)
    // TODO: Implement restore functionality
  }

  const handleCreateBranch = (versionId: string) => {
    console.log('Create branch from version:', versionId)
    // TODO: Implement branch creation
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Version History</h1>
          <p className="text-muted-foreground mt-2">
            Track changes and manage different versions of your project
          </p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Create Version
        </Button>
      </div>

      {/* Version Timeline */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="space-y-3">
                    <div className="h-4 bg-muted rounded w-1/4" />
                    <div className="h-3 bg-muted rounded w-3/4" />
                    <div className="h-3 bg-muted rounded w-1/2" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-8 top-0 bottom-0 w-px bg-border" />
            
            {versions.map((version, index) => (
              <div key={version.id} className="relative flex gap-6 pb-8">
                {/* Timeline dot */}
                <div className={`relative z-10 flex h-16 w-16 items-center justify-center rounded-full border-4 ${
                  version.is_current 
                    ? 'bg-primary border-primary' 
                    : 'bg-background border-border'
                }`}>
                  {version.is_current ? (
                    <GitCommit className="h-6 w-6 text-primary-foreground" />
                  ) : (
                    <Tag className="h-6 w-6 text-muted-foreground" />
                  )}
                </div>

                {/* Version card */}
                <Card className={`flex-1 ${version.is_current ? 'ring-2 ring-primary' : ''}`}>
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <div className="flex items-center gap-3">
                          <CardTitle className="text-lg">{version.title}</CardTitle>
                          <Badge variant={version.is_current ? 'default' : 'secondary'}>
                            {version.version}
                          </Badge>
                          {version.is_current && (
                            <Badge variant="outline">Current</Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {version.description}
                        </p>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Eye className="h-4 w-4 mr-2" />
                            View Files
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Download className="h-4 w-4 mr-2" />
                            Download
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleCreateBranch(version.id)}>
                            <GitBranch className="h-4 w-4 mr-2" />
                            Create Branch
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Copy className="h-4 w-4 mr-2" />
                            Duplicate
                          </DropdownMenuItem>
                          {!version.is_current && (
                            <>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                onClick={() => handleRestoreVersion(version.id)}
                                className="text-orange-600"
                              >
                                <Restore className="h-4 w-4 mr-2" />
                                Restore
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <span>{version.author.name}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span>{formatDate(version.created_at)}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <GitCommit className="h-4 w-4 text-muted-foreground" />
                        <span>{getChangesSummary(version.changes)} files</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-muted-foreground">Size:</span>
                        <span>{formatFileSize(version.size)}</span>
                      </div>
                    </div>

                    {/* Changes breakdown */}
                    <div className="mt-4 flex gap-4 text-xs">
                      {version.changes.files_added > 0 && (
                        <span className="text-green-600">
                          +{version.changes.files_added} added
                        </span>
                      )}
                      {version.changes.files_modified > 0 && (
                        <span className="text-blue-600">
                          ~{version.changes.files_modified} modified
                        </span>
                      )}
                      {version.changes.files_deleted > 0 && (
                        <span className="text-red-600">
                          -{version.changes.files_deleted} deleted
                        </span>
                      )}
                    </div>

                    {/* Actions */}
                    <div className="mt-4 flex gap-2">
                      <Button variant="outline" size="sm">
                        <Eye className="h-3 w-3 mr-2" />
                        View
                      </Button>
                      <Button variant="outline" size="sm">
                        <Download className="h-3 w-3 mr-2" />
                        Download
                      </Button>
                      {!version.is_current && (
                        <Button variant="outline" size="sm">
                          <ArrowRight className="h-3 w-3 mr-2" />
                          Compare
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Version Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Total Versions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{versions.length}</div>
            <p className="text-xs text-muted-foreground">
              Across all time
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Latest Version</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {versions.find(v => v.is_current)?.version || 'N/A'}
            </div>
            <p className="text-xs text-muted-foreground">
              Current active version
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Total Size</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatFileSize(versions.reduce((acc, v) => acc + v.size, 0))}
            </div>
            <p className="text-xs text-muted-foreground">
              All versions combined
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
