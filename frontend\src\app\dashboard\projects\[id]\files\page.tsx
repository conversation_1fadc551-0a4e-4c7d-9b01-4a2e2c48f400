'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  FileText,
  Folder,
  FolderOpen,
  Code,
  Image,
  Download,
  Eye,
  Edit,
  Trash2,
  Plus,
  Search,
  MoreVertical,
  ArrowLeft,
  Home,
  ChevronRight,
} from 'lucide-react'

interface FileItem {
  id: string
  name: string
  type: 'file' | 'folder'
  size?: number
  modified: string
  content?: string
  language?: string
  parent?: string
}

interface Project {
  id: number
  name: string
}

export default function FilesPage() {
  const params = useParams()
  const [project, setProject] = useState<Project | null>(null)
  const [files, setFiles] = useState<FileItem[]>([])
  const [currentPath, setCurrentPath] = useState<string[]>([])
  const [selectedFile, setSelectedFile] = useState<FileItem | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Mock project data
    setProject({
      id: parseInt(params.id as string),
      name: 'My Portfolio Website'
    })

    // Mock file structure
    const mockFiles: FileItem[] = [
      {
        id: '1',
        name: 'index.html',
        type: 'file',
        size: 2048,
        modified: '2024-01-20T14:30:00Z',
        language: 'html',
        content: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>John Doe - Portfolio</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <nav>
            <h1>John Doe</h1>
            <ul>
                <li><a href="#about">About</a></li>
                <li><a href="#projects">Projects</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </nav>
    </header>
    
    <main>
        <section id="hero">
            <h2>Full Stack Developer</h2>
            <p>Building amazing web experiences</p>
        </section>
    </main>
    
    <script src="script.js"></script>
</body>
</html>`
      },
      {
        id: '2',
        name: 'styles.css',
        type: 'file',
        size: 1536,
        modified: '2024-01-20T14:25:00Z',
        language: 'css',
        content: `* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
}

header {
    background: #2c3e50;
    color: white;
    padding: 1rem 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

#hero {
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}`
      },
      {
        id: '3',
        name: 'script.js',
        type: 'file',
        size: 512,
        modified: '2024-01-20T14:20:00Z',
        language: 'javascript',
        content: `// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        
        document.querySelector(this.getAttribute('href')).scrollIntoView({
            behavior: 'smooth'
        });
    });
});

// Add scroll effect to header
window.addEventListener('scroll', () => {
    const header = document.querySelector('header');
    if (window.scrollY > 100) {
        header.style.background = 'rgba(44, 62, 80, 0.95)';
    } else {
        header.style.background = '#2c3e50';
    }
});`
      },
      {
        id: '4',
        name: 'assets',
        type: 'folder',
        modified: '2024-01-20T14:15:00Z'
      },
      {
        id: '5',
        name: 'components',
        type: 'folder',
        modified: '2024-01-20T14:10:00Z'
      }
    ]

    setTimeout(() => {
      setFiles(mockFiles)
      setIsLoading(false)
    }, 1000)
  }, [params.id])

  const getCurrentFiles = () => {
    if (currentPath.length === 0) {
      return files.filter(file => !file.parent)
    }
    const currentFolder = currentPath[currentPath.length - 1]
    return files.filter(file => file.parent === currentFolder)
  }

  const filteredFiles = getCurrentFiles().filter(file =>
    file.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getFileIcon = (file: FileItem) => {
    if (file.type === 'folder') {
      return <Folder className="w-4 h-4 text-blue-500" />
    }

    const extension = file.name.split('.').pop()?.toLowerCase()
    switch (extension) {
      case 'html':
      case 'htm':
        return <Code className="w-4 h-4 text-orange-500" />
      case 'css':
        return <Code className="w-4 h-4 text-blue-500" />
      case 'js':
      case 'ts':
        return <Code className="w-4 h-4 text-yellow-500" />
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'svg':
        return <Image className="w-4 h-4 text-green-500" />
      default:
        return <FileText className="w-4 h-4 text-gray-500" />
    }
  }

  const handleFileClick = (file: FileItem) => {
    if (file.type === 'folder') {
      setCurrentPath([...currentPath, file.id])
    } else {
      setSelectedFile(file)
    }
  }

  const navigateToPath = (index: number) => {
    setCurrentPath(currentPath.slice(0, index + 1))
  }

  const goBack = () => {
    setCurrentPath(currentPath.slice(0, -1))
  }

  return (
    <div className="flex h-[calc(100vh-8rem)]">
      {/* File Explorer */}
      <div className="w-1/2 border-r border-border flex flex-col">
        {/* Header */}
        <div className="border-b border-border p-4">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-xl font-semibold">Files</h1>
            <Button size="sm">
              <Plus className="w-4 h-4 mr-2" />
              New File
            </Button>
          </div>

          {/* Breadcrumb */}
          <div className="flex items-center gap-1 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCurrentPath([])}
              className="h-8 px-2"
            >
              <Home className="w-4 h-4" />
            </Button>
            {currentPath.map((pathId, index) => {
              const folder = files.find(f => f.id === pathId)
              return (
                <div key={pathId} className="flex items-center">
                  <ChevronRight className="w-4 h-4 text-muted-foreground" />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => navigateToPath(index)}
                    className="h-8 px-2"
                  >
                    {folder?.name}
                  </Button>
                </div>
              )
            })}
          </div>

          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search files..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* File List */}
        <ScrollArea className="flex-1">
          <div className="p-4">
            {currentPath.length > 0 && (
              <div
                className="flex items-center gap-3 p-2 hover:bg-accent rounded-lg cursor-pointer mb-2"
                onClick={goBack}
              >
                <ArrowLeft className="w-4 h-4" />
                <span className="text-sm">Back</span>
              </div>
            )}

            {isLoading ? (
              <div className="space-y-2">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="h-12 bg-muted animate-pulse rounded" />
                ))}
              </div>
            ) : filteredFiles.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No files found</p>
              </div>
            ) : (
              <div className="space-y-1">
                {filteredFiles.map((file) => (
                  <div
                    key={file.id}
                    className={`flex items-center gap-3 p-3 hover:bg-accent rounded-lg cursor-pointer transition-colors ${
                      selectedFile?.id === file.id ? 'bg-accent' : ''
                    }`}
                    onClick={() => handleFileClick(file)}
                  >
                    {getFileIcon(file)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="font-medium truncate">{file.name}</span>
                        {file.language && (
                          <Badge variant="outline" className="text-xs">
                            {file.language}
                          </Badge>
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {file.size && formatFileSize(file.size)} • {formatDate(file.modified)}
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-destructive">
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                ))}
              </div>
            )}
          </div>
        </ScrollArea>
      </div>

      {/* File Viewer */}
      <div className="w-1/2 flex flex-col">
        {selectedFile ? (
          <>
            <div className="border-b border-border p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getFileIcon(selectedFile)}
                  <div>
                    <h2 className="font-semibold">{selectedFile.name}</h2>
                    <p className="text-sm text-muted-foreground">
                      {selectedFile.size && formatFileSize(selectedFile.size)} • 
                      Modified {formatDate(selectedFile.modified)}
                    </p>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Edit className="w-4 h-4 mr-2" />
                    Edit
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="w-4 h-4 mr-2" />
                    Download
                  </Button>
                </div>
              </div>
            </div>
            <ScrollArea className="flex-1">
              <div className="p-4">
                <pre className="text-sm bg-muted p-4 rounded-lg overflow-x-auto">
                  <code>{selectedFile.content || 'No content available'}</code>
                </pre>
              </div>
            </ScrollArea>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Select a file</h3>
              <p className="text-muted-foreground">
                Choose a file from the explorer to view its contents
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
