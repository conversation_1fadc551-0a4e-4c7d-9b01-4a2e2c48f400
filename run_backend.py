#!/usr/bin/env python3
"""
Backend server runner for Codora.
"""

import uvicorn
from backend.main import app

if __name__ == "__main__":
    print("🚀 Starting Codora Backend Server...")
    print("📍 Server will be available at: http://localhost:8000")
    print("📚 API Documentation at: http://localhost:8000/docs")
    print("🔄 Auto-reload enabled for development")
    print("\nPress Ctrl+C to stop the server\n")
    
    uvicorn.run(
        "backend.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
