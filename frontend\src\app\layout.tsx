import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { cn } from '@/lib/utils'
import { ThemeProvider } from '@/components/theme-provider'
import { ThemeScript } from '@/components/theme-script'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Codora - AI Web Development Assistant',
  description: 'Build websites with AI assistance using natural language',
  keywords: ['AI', 'web development', 'assistant', 'coding', 'automation'],
  authors: [{ name: 'Codora Team' }],
}

export function generateViewport() {
  return {
    width: 'device-width',
    initialScale: 1,
    themeColor: [
      { media: '(prefers-color-scheme: light)', color: '#0f172a' },
      { media: '(prefers-color-scheme: dark)', color: '#0f172a' },
    ],
  }
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <ThemeScript />
      </head>
      <body className={cn(inter.className, "min-h-screen bg-background text-foreground antialiased")}>
        <ThemeProvider defaultTheme="dark" storageKey="codora-ui-theme">
          <div id="root" className="relative flex min-h-screen flex-col bg-background">
            {children}
          </div>
        </ThemeProvider>
      </body>
    </html>
  )
}
