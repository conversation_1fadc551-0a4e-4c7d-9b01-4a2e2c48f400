import axios, { AxiosInstance, AxiosResponse } from 'axios'

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

class ApiClient {
  private client: AxiosInstance

  constructor() {
    this.client = axios.create({
      baseURL: API_URL,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = this.getToken()
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // Response interceptor to handle auth errors
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          this.removeToken()
          window.location.href = '/auth/login'
        }
        return Promise.reject(error)
      }
    )
  }

  private getToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('auth_token')
    }
    return null
  }

  private setToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token)
    }
  }

  private removeToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token')
    }
  }

  // Auth methods
  async register(userData: {
    full_name: string
    username: string
    email: string
    password: string
  }) {
    const response = await this.client.post('/auth/register', userData)
    console.log('Register response:', response.data) // Debug log
    if (response.data.access_token) {
      this.setToken(response.data.access_token)
    }
    return response.data
  }

  async login(credentials: {
    username_or_email: string
    password: string
  }) {
    const response = await this.client.post('/auth/login', credentials)
    console.log('Login response:', response.data) // Debug log
    if (response.data.access_token) {
      this.setToken(response.data.access_token)
    }
    return response.data
  }

  async getCurrentUser() {
    const response = await this.client.get('/auth/me')
    return response.data
  }

  logout() {
    this.removeToken()
  }

  // Project methods
  async getProjects() {
    const response = await this.client.get('/projects/')
    return response.data
  }

  async createProject(projectData: {
    name: string
    description?: string
  }) {
    const response = await this.client.post('/projects/', projectData)
    return response.data
  }

  async getProject(projectId: number) {
    const response = await this.client.get(`/projects/${projectId}`)
    return response.data
  }

  async updateProject(projectId: number, projectData: {
    name?: string
    description?: string
    is_public?: boolean
  }) {
    const response = await this.client.put(`/projects/${projectId}`, projectData)
    return response.data
  }

  async deleteProject(projectId: number) {
    const response = await this.client.delete(`/projects/${projectId}`)
    return response.data
  }

  // Chat methods
  async sendMessage(projectId: number, content: string) {
    // This returns a stream, so we handle it differently
    const response = await fetch(`${API_URL}/chat/${projectId}/message`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.getToken()}`,
      },
      body: JSON.stringify({ content }),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response
  }

  async getChatHistory(projectId: number) {
    const response = await this.client.get(`/chat/${projectId}/history`)
    return response.data
  }

  async getVersionPreview(projectId: number, versionId: number) {
    const response = await this.client.get(`/chat/${projectId}/versions/${versionId}/preview`)
    return response.data
  }

  // Sharing methods
  async getSharedProject(token: string) {
    const response = await this.client.get(`/projects/shared/${token}`)
    return response.data
  }

  async forkProject(token: string) {
    const response = await this.client.post(`/projects/fork/${token}`)
    return response.data
  }

  // Health check
  async healthCheck() {
    const response = await this.client.get('/health')
    return response.data
  }
}

export const apiClient = new ApiClient()
export default apiClient
