"""
File system tools for the AI web development assistant agent.
Each tool handles file operations with proper error handling.
"""

import re
import shutil
from pathlib import Path
from langchain_core.tools import tool


class FileSystemTools:
    """Collection of file system tools for the agent."""
    
    def __init__(self, project_root: str = "projects"):
        """Initialize with a project root directory."""
        self.project_root = Path(project_root)
        self.project_root.mkdir(exist_ok=True)
    
    def _normalize_path(self, path: str) -> Path:
        """Normalize path to be relative to project root and ensure it starts with /."""
        if not path.startswith('/'):
            path = '/' + path
        # Remove leading slash for Path operations
        clean_path = path.lstrip('/')
        return self.project_root / clean_path
    
    def _ensure_sandboxed(self, path: Path) -> bool:
        """Ensure the path is within the project root (security check)."""
        try:
            path.resolve().relative_to(self.project_root.resolve())
            return True
        except ValueError:
            return False
    
    def create_file(self, path: str, content: str) -> str:
        """
        Creates a new file at the given path and writes the specified content.
        Automatically creates parent folders if they don't exist.
        """
        try:
            file_path = self._normalize_path(path)
            
            if not self._ensure_sandboxed(file_path):
                return f"Error: Path '{path}' is outside the project directory"
            
            # Create parent directories if they don't exist
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write content to file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return f"Successfully created file: {path}"
            
        except Exception as e:
            return f"Error creating file '{path}': {str(e)}"
    
    def create_folder(self, path: str) -> str:
        """Creates a new folder at the given path."""
        try:
            folder_path = self._normalize_path(path)
            
            if not self._ensure_sandboxed(folder_path):
                return f"Error: Path '{path}' is outside the project directory"
            
            folder_path.mkdir(parents=True, exist_ok=True)
            return f"Successfully created folder: {path}"
            
        except Exception as e:
            return f"Error creating folder '{path}': {str(e)}"
    
    def read_file(self, path: str) -> str:
        """Reads and returns the content of the file."""
        try:
            file_path = self._normalize_path(path)
            
            if not self._ensure_sandboxed(file_path):
                return f"Error: Path '{path}' is outside the project directory"
            
            if not file_path.exists():
                return f"Error: File '{path}' does not exist"
            
            if not file_path.is_file():
                return f"Error: '{path}' is not a file"
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return f"Content of {path}:\n{content}"
            
        except Exception as e:
            return f"Error reading file '{path}': {str(e)}"
    
    def list_directory(self, path: str = ".") -> str:
        """Lists the contents of the specified folder."""
        try:
            dir_path = self._normalize_path(path)
            
            if not self._ensure_sandboxed(dir_path):
                return f"Error: Path '{path}' is outside the project directory"
            
            if not dir_path.exists():
                return f"Error: Directory '{path}' does not exist"
            
            if not dir_path.is_dir():
                return f"Error: '{path}' is not a directory"
            
            items = []
            for item in sorted(dir_path.iterdir()):
                item_type = "📁" if item.is_dir() else "📄"
                relative_path = item.relative_to(self.project_root)
                items.append(f"{item_type} {relative_path}")
            
            if not items:
                return f"Directory '{path}' is empty"
            
            return f"Contents of {path}:\n" + "\n".join(items)
            
        except Exception as e:
            return f"Error listing directory '{path}': {str(e)}"
    
    def replace_in_file(self, path: str, pattern: str, replacement: str) -> str:
        """Finds and replaces text (using regex pattern) in the file content."""
        try:
            file_path = self._normalize_path(path)
            
            if not self._ensure_sandboxed(file_path):
                return f"Error: Path '{path}' is outside the project directory"
            
            if not file_path.exists():
                return f"Error: File '{path}' does not exist"
            
            if not file_path.is_file():
                return f"Error: '{path}' is not a file"
            
            # Read current content
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Perform replacement
            try:
                new_content = re.sub(pattern, replacement, content)
                matches = len(re.findall(pattern, content))
            except re.error as e:
                return f"Error: Invalid regex pattern '{pattern}': {str(e)}"
            
            # Write back to file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            return f"Successfully replaced {matches} occurrence(s) in {path}"
            
        except Exception as e:
            return f"Error replacing in file '{path}': {str(e)}"
    
    def search_in_files(self, path: str, pattern: str) -> str:
        """Searches all files under the given folder for a regex pattern."""
        try:
            search_path = self._normalize_path(path)
            
            if not self._ensure_sandboxed(search_path):
                return f"Error: Path '{path}' is outside the project directory"
            
            if not search_path.exists():
                return f"Error: Path '{path}' does not exist"
            
            results = {}
            
            # Search in files
            if search_path.is_file():
                files_to_search = [search_path]
            else:
                files_to_search = [f for f in search_path.rglob('*') if f.is_file()]
            
            for file_path in files_to_search:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    matches = re.findall(pattern, content, re.MULTILINE)
                    if matches:
                        relative_path = file_path.relative_to(self.project_root)
                        results[str(relative_path)] = matches
                        
                except (UnicodeDecodeError, PermissionError):
                    # Skip binary files or files we can't read
                    continue
                except re.error as e:
                    return f"Error: Invalid regex pattern '{pattern}': {str(e)}"
            
            if not results:
                return f"No matches found for pattern '{pattern}' in {path}"
            
            result_text = f"Search results for pattern '{pattern}' in {path}:\n"
            for file_path, matches in results.items():
                result_text += f"\n📄 {file_path}:\n"
                for match in matches[:5]:  # Limit to first 5 matches per file
                    result_text += f"  - {match}\n"
                if len(matches) > 5:
                    result_text += f"  ... and {len(matches) - 5} more matches\n"
            
            return result_text
            
        except Exception as e:
            return f"Error searching in '{path}': {str(e)}"
    
    def move_file_or_folder(self, source_path: str, destination_path: str) -> str:
        """Moves a file or folder to a new location."""
        try:
            src_path = self._normalize_path(source_path)
            dst_path = self._normalize_path(destination_path)
            
            if not self._ensure_sandboxed(src_path) or not self._ensure_sandboxed(dst_path):
                return f"Error: Paths must be within the project directory"
            
            if not src_path.exists():
                return f"Error: Source '{source_path}' does not exist"
            
            # Create destination parent directory if needed
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.move(str(src_path), str(dst_path))
            return f"Successfully moved '{source_path}' to '{destination_path}'"
            
        except Exception as e:
            return f"Error moving '{source_path}' to '{destination_path}': {str(e)}"
    
    def delete_file_or_folder(self, path: str) -> str:
        """Deletes a file or folder."""
        try:
            target_path = self._normalize_path(path)
            
            if not self._ensure_sandboxed(target_path):
                return f"Error: Path '{path}' is outside the project directory"
            
            if not target_path.exists():
                return f"Error: '{path}' does not exist"
            
            if target_path.is_file():
                target_path.unlink()
                return f"Successfully deleted file: {path}"
            elif target_path.is_dir():
                shutil.rmtree(target_path)
                return f"Successfully deleted folder: {path}"
            else:
                return f"Error: '{path}' is neither a file nor a directory"
                
        except Exception as e:
            return f"Error deleting '{path}': {str(e)}"
    
    def update_file(self, path: str, new_content: str) -> str:
        """Updates a file with new content (overwrites existing content)."""
        try:
            file_path = self._normalize_path(path)
            
            if not self._ensure_sandboxed(file_path):
                return f"Error: Path '{path}' is outside the project directory"
            
            if not file_path.exists():
                return f"Error: File '{path}' does not exist"
            
            if not file_path.is_file():
                return f"Error: '{path}' is not a file"
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            return f"Successfully updated file: {path}"
            
        except Exception as e:
            return f"Error updating file '{path}': {str(e)}"


# Create a global instance for easy access
fs_tools = FileSystemTools()


# Tool functions for LangGraph (these will be used by the agent)
@tool
def create_file(path: str, content: str) -> str:
    """Creates a new file at the given path and writes the specified content."""
    return fs_tools.create_file(path, content)

@tool
def create_folder(path: str) -> str:
    """Creates a new folder at the given path."""
    return fs_tools.create_folder(path)

@tool
def read_file(path: str) -> str:
    """Reads and returns the content of the file."""
    return fs_tools.read_file(path)

@tool
def list_directory(path: str = ".") -> str:
    """Lists the contents of the specified folder."""
    return fs_tools.list_directory(path)

@tool
def replace_in_file(path: str, pattern: str, replacement: str) -> str:
    """Finds and replaces text (using regex pattern) in the file content."""
    return fs_tools.replace_in_file(path, pattern, replacement)

@tool
def search_in_files(path: str, pattern: str) -> str:
    """Searches all files under the given folder for a regex pattern."""
    return fs_tools.search_in_files(path, pattern)

@tool
def move_file_or_folder(source_path: str, destination_path: str) -> str:
    """Moves a file or folder to a new location."""
    return fs_tools.move_file_or_folder(source_path, destination_path)

@tool
def delete_file_or_folder(path: str) -> str:
    """Deletes a file or folder."""
    return fs_tools.delete_file_or_folder(path)

@tool
def update_file(path: str, new_content: str) -> str:
    """Updates a file with new content (overwrites existing content)."""
    return fs_tools.update_file(path, new_content)