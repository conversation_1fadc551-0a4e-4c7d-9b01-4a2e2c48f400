# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/codora

# JWT Configuration
SECRET_KEY=your-super-secret-key-here-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AI Model Configuration
OPENAI_API_KEY=your-openai-api-key-here
MODEL_NAME=gpt-4o-mini
MODEL_PROVIDER=openai

# Application Configuration
DEBUG=True
CORS_ORIGINS=["http://localhost:3000"]
