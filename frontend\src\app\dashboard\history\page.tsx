'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Search,
  Clock,
  FolderOpen,
  MessageSquare,
  GitBranch,
  Star,
  GitFork,
  Eye,
  Edit,
  Download,
  Calendar,
  Filter,
  MoreVertical,
} from 'lucide-react'

interface HistoryItem {
  id: string
  type: 'project_created' | 'project_updated' | 'message_sent' | 'version_created' | 'project_starred' | 'project_forked' | 'file_created' | 'file_updated'
  title: string
  description: string
  timestamp: string
  project: {
    id: number
    name: string
  }
  metadata?: {
    version?: string
    filename?: string
    message_count?: number
  }
}

export default function HistoryPage() {
  const [history, setHistory] = useState<HistoryItem[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [activeTab, setActiveTab] = useState('all')
  const [timeFilter, setTimeFilter] = useState<'all' | 'today' | 'week' | 'month'>('all')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Mock history data
    const mockHistory: HistoryItem[] = [
      {
        id: '1',
        type: 'message_sent',
        title: 'Added contact form functionality',
        description: 'Implemented a fully functional contact form with validation',
        timestamp: '2024-01-20T14:30:00Z',
        project: { id: 1, name: 'My Portfolio Website' },
        metadata: { message_count: 23 }
      },
      {
        id: '2',
        type: 'version_created',
        title: 'Created version 1.4.0',
        description: 'Added contact form and animations',
        timestamp: '2024-01-20T14:25:00Z',
        project: { id: 1, name: 'My Portfolio Website' },
        metadata: { version: '1.4.0' }
      },
      {
        id: '3',
        type: 'file_updated',
        title: 'Updated styles.css',
        description: 'Enhanced responsive design and animations',
        timestamp: '2024-01-20T14:20:00Z',
        project: { id: 1, name: 'My Portfolio Website' },
        metadata: { filename: 'styles.css' }
      },
      {
        id: '4',
        type: 'project_starred',
        title: 'Starred Creative Portfolio Showcase',
        description: 'Added to favorites for future reference',
        timestamp: '2024-01-18T17:00:00Z',
        project: { id: 102, name: 'Creative Portfolio Showcase' }
      },
      {
        id: '5',
        type: 'project_forked',
        title: 'Forked SaaS Landing Page',
        description: 'Created a copy to customize for own use',
        timestamp: '2024-01-18T16:45:00Z',
        project: { id: 103, name: 'SaaS Landing Page' }
      },
      {
        id: '6',
        type: 'message_sent',
        title: 'Enhanced mobile responsiveness',
        description: 'Improved layout for tablet and mobile devices',
        timestamp: '2024-01-18T16:20:00Z',
        project: { id: 1, name: 'My Portfolio Website' },
        metadata: { message_count: 22 }
      },
      {
        id: '7',
        type: 'version_created',
        title: 'Created version 1.3.0',
        description: 'Enhanced responsive design',
        timestamp: '2024-01-18T16:15:00Z',
        project: { id: 1, name: 'My Portfolio Website' },
        metadata: { version: '1.3.0' }
      },
      {
        id: '8',
        type: 'project_created',
        title: 'Created Blog Website',
        description: 'Started a new blog project with CMS integration',
        timestamp: '2024-01-15T11:00:00Z',
        project: { id: 4, name: 'Blog Website' }
      },
      {
        id: '9',
        type: 'file_created',
        title: 'Created index.html',
        description: 'Initial HTML structure for the portfolio',
        timestamp: '2024-01-15T10:30:00Z',
        project: { id: 1, name: 'My Portfolio Website' },
        metadata: { filename: 'index.html' }
      },
      {
        id: '10',
        type: 'project_created',
        title: 'Created My Portfolio Website',
        description: 'Started building a personal portfolio website',
        timestamp: '2024-01-15T10:00:00Z',
        project: { id: 1, name: 'My Portfolio Website' }
      }
    ]

    setTimeout(() => {
      setHistory(mockHistory)
      setIsLoading(false)
    }, 1000)
  }, [])

  const getFilteredHistory = () => {
    let filtered = history

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(item =>
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.project.name.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Filter by type
    if (activeTab !== 'all') {
      filtered = filtered.filter(item => {
        switch (activeTab) {
          case 'projects':
            return ['project_created', 'project_updated', 'project_starred', 'project_forked'].includes(item.type)
          case 'messages':
            return item.type === 'message_sent'
          case 'versions':
            return item.type === 'version_created'
          case 'files':
            return ['file_created', 'file_updated'].includes(item.type)
          default:
            return true
        }
      })
    }

    // Filter by time
    if (timeFilter !== 'all') {
      const now = new Date()
      const filterDate = new Date()
      
      switch (timeFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0)
          break
        case 'week':
          filterDate.setDate(now.getDate() - 7)
          break
        case 'month':
          filterDate.setMonth(now.getMonth() - 1)
          break
      }

      filtered = filtered.filter(item => new Date(item.timestamp) >= filterDate)
    }

    return filtered
  }

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60)
      return `${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''} ago`
    } else if (diffInHours < 24) {
      const hours = Math.floor(diffInHours)
      return `${hours} hour${hours !== 1 ? 's' : ''} ago`
    } else if (diffInHours < 168) { // 7 days
      const days = Math.floor(diffInHours / 24)
      return `${days} day${days !== 1 ? 's' : ''} ago`
    } else {
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    }
  }

  const getActivityIcon = (type: HistoryItem['type']) => {
    switch (type) {
      case 'project_created':
      case 'project_updated':
        return <FolderOpen className="h-4 w-4 text-blue-500" />
      case 'message_sent':
        return <MessageSquare className="h-4 w-4 text-green-500" />
      case 'version_created':
        return <GitBranch className="h-4 w-4 text-purple-500" />
      case 'project_starred':
        return <Star className="h-4 w-4 text-yellow-500" />
      case 'project_forked':
        return <GitFork className="h-4 w-4 text-orange-500" />
      case 'file_created':
      case 'file_updated':
        return <Edit className="h-4 w-4 text-indigo-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getActivityColor = (type: HistoryItem['type']) => {
    switch (type) {
      case 'project_created':
      case 'project_updated':
        return 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950'
      case 'message_sent':
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950'
      case 'version_created':
        return 'border-purple-200 bg-purple-50 dark:border-purple-800 dark:bg-purple-950'
      case 'project_starred':
        return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950'
      case 'project_forked':
        return 'border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950'
      case 'file_created':
      case 'file_updated':
        return 'border-indigo-200 bg-indigo-50 dark:border-indigo-800 dark:bg-indigo-950'
      default:
        return 'border-gray-200 bg-gray-50 dark:border-gray-800 dark:bg-gray-950'
    }
  }

  const filteredHistory = getFilteredHistory()
  const groupedByDate = filteredHistory.reduce((groups, item) => {
    const date = new Date(item.timestamp).toDateString()
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(item)
    return groups
  }, {} as Record<string, HistoryItem[]>)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Activity History</h1>
          <p className="text-muted-foreground mt-2">
            Track all your activities and changes across projects
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search activities..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          {(['all', 'today', 'week', 'month'] as const).map((filter) => (
            <Button
              key={filter}
              variant={timeFilter === filter ? 'default' : 'outline'}
              size="sm"
              onClick={() => setTimeFilter(filter)}
              className="capitalize"
            >
              {filter === 'all' ? 'All Time' : filter}
            </Button>
          ))}
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="all">All Activities</TabsTrigger>
          <TabsTrigger value="projects">Projects</TabsTrigger>
          <TabsTrigger value="messages">Messages</TabsTrigger>
          <TabsTrigger value="versions">Versions</TabsTrigger>
          <TabsTrigger value="files">Files</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-6">
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(8)].map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardContent className="p-4">
                    <div className="flex gap-4">
                      <div className="h-10 w-10 bg-muted rounded-full" />
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-muted rounded w-3/4" />
                        <div className="h-3 bg-muted rounded w-1/2" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredHistory.length === 0 ? (
            <div className="text-center py-12">
              <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">
                No activities found
              </h3>
              <p className="text-muted-foreground">
                {searchQuery ? 'Try adjusting your search or filters' : 'Start working on projects to see your activity here'}
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              {Object.entries(groupedByDate).map(([date, items]) => (
                <div key={date}>
                  <div className="flex items-center gap-2 mb-4">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <h3 className="font-medium text-foreground">
                      {new Date(date).toLocaleDateString('en-US', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </h3>
                    <div className="flex-1 h-px bg-border" />
                  </div>
                  
                  <div className="space-y-3">
                    {items.map((item) => (
                      <Card key={item.id} className={`border-l-4 ${getActivityColor(item.type)}`}>
                        <CardContent className="p-4">
                          <div className="flex gap-4">
                            <div className="flex-shrink-0 mt-1">
                              {getActivityIcon(item.type)}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <h4 className="font-medium text-foreground mb-1">
                                    {item.title}
                                  </h4>
                                  <p className="text-sm text-muted-foreground mb-2">
                                    {item.description}
                                  </p>
                                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                    <Link 
                                      href={`/dashboard/projects/${item.project.id}`}
                                      className="hover:text-primary transition-colors"
                                    >
                                      {item.project.name}
                                    </Link>
                                    <span>{formatTimestamp(item.timestamp)}</span>
                                    {item.metadata?.version && (
                                      <Badge variant="outline" className="text-xs">
                                        v{item.metadata.version}
                                      </Badge>
                                    )}
                                    {item.metadata?.filename && (
                                      <Badge variant="outline" className="text-xs">
                                        {item.metadata.filename}
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                                <Button variant="ghost" size="sm" asChild>
                                  <Link href={`/dashboard/projects/${item.project.id}`}>
                                    <Eye className="h-3 w-3 mr-1" />
                                    View
                                  </Link>
                                </Button>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
