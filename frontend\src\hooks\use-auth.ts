'use client'

import { useEffect, useState } from 'react'
import { useAuthStore } from '@/store/auth'

export function useAuth() {
  const store = useAuthStore()
  const [isHydrated, setIsHydrated] = useState(false)

  useEffect(() => {
    // Check if we're on the client side and store has hydrated
    const checkHydration = () => {
      if (typeof window !== 'undefined') {
        // Check if localStorage has auth data
        const authStorage = localStorage.getItem('auth-storage')
        if (authStorage) {
          // Small delay to ensure Zustand has hydrated
          setTimeout(() => {
            setIsHydrated(true)
          }, 50)
        } else {
          // No auth data, can hydrate immediately
          setIsHydrated(true)
        }
      }
    }

    checkHydration()
  }, [])

  return {
    ...store,
    isHydrated,
    // Helper to check if user is truly authenticated (after hydration)
    isReady: isHydrated && !store.isLoading,
    isLoggedIn: isHydrated && store.isAuthenticated && !!store.token,
  }
}
