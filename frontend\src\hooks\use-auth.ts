'use client'

import { useEffect, useState } from 'react'
import { useAuthStore } from '@/store/auth'

export function useAuth() {
  const store = useAuthStore()
  const [isHydrated, setIsHydrated] = useState(false)

  useEffect(() => {
    // Check if we're on the client side and store has hydrated
    const checkHydration = () => {
      if (typeof window !== 'undefined') {
        // Small delay to ensure Zustand has hydrated
        setTimeout(() => {
          setIsHydrated(true)
        }, 100)
      }
    }

    checkHydration()
  }, [])

  return {
    ...store,
    isHydrated,
    // Helper to check if user is truly authenticated (after hydration)
    isReady: isHydrated && !store.isLoading,
    isLoggedIn: isHydrated && store.isAuthenticated && !!store.token,
  }
}
