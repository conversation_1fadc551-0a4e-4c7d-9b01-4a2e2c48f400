import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User } from '@/types'
import apiClient from '@/lib/api'

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  hasHydrated: boolean
}

interface AuthActions {
  login: (credentials: { username_or_email: string; password: string }) => Promise<void>
  register: (userData: {
    full_name: string
    username: string
    email: string
    password: string
  }) => Promise<void>
  logout: () => void
  getCurrentUser: () => Promise<void>
  clearError: () => void
  setLoading: (loading: boolean) => void
}

type AuthStore = AuthState & AuthActions

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      hasHydrated: false,

      // Actions
      login: async (credentials) => {
        set({ isLoading: true, error: null })
        try {
          const response = await apiClient.login(credentials)
          console.log('Auth store login response:', response) // Debug log
          set({
            user: response.user,
            token: response.access_token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          })
        } catch (error: any) {
          console.error('Auth store login error:', error) // Debug log
          set({
            error: error.response?.data?.detail || 'Login failed',
            isLoading: false,
          })
          throw error
        }
      },

      register: async (userData) => {
        set({ isLoading: true, error: null })
        try {
          const response = await apiClient.register(userData)
          console.log('Auth store register response:', response) // Debug log
          set({
            user: response.user,
            token: response.access_token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          })
        } catch (error: any) {
          console.error('Auth store register error:', error) // Debug log
          set({
            error: error.response?.data?.detail || 'Registration failed',
            isLoading: false,
          })
          throw error
        }
      },

      logout: () => {
        apiClient.logout()
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          error: null,
        })
      },

      getCurrentUser: async () => {
        const { token } = get()
        if (!token) {
          console.log('No token in getCurrentUser') // Debug log
          return
        }

        set({ isLoading: true })
        try {
          const user = await apiClient.getCurrentUser()
          console.log('getCurrentUser success:', user) // Debug log
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          })
        } catch (error: any) {
          console.error('getCurrentUser error:', error) // Debug log
          // If token is invalid, logout
          if (error.response?.status === 401) {
            console.log('Token invalid, logging out') // Debug log
            get().logout()
          } else {
            set({
              error: error.response?.data?.detail || 'Failed to get user info',
              isLoading: false,
            })
          }
        }
      },

      clearError: () => set({ error: null }),

      setLoading: (loading: boolean) => set({ isLoading: loading }),
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => (state) => {
        console.log('Auth store rehydrated:', state) // Debug log
        if (state?.token) {
          console.log('Token found in storage:', !!state.token) // Debug log
          // Set hasHydrated to true after rehydration
          state.hasHydrated = true
        }
      },
    }
  )
)
