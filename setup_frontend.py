#!/usr/bin/env python3
"""
Frontend setup script for Codora.
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(command, description, cwd=None):
    """Run a command and handle errors."""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True, cwd=cwd)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"Error: {e.stderr}")
        return False

def main():
    """Main setup function."""
    print("🚀 Setting up Codora Frontend...")
    
    # Check if Node.js is installed
    try:
        result = subprocess.run("node --version", shell=True, check=True, capture_output=True, text=True)
        node_version = result.stdout.strip()
        print(f"✅ Node.js found: {node_version}")
    except subprocess.CalledProcessError:
        print("❌ Node.js not found. Please install Node.js 18+ from https://nodejs.org/")
        return False
    
    # Check if npm is installed
    try:
        result = subprocess.run("npm --version", shell=True, check=True, capture_output=True, text=True)
        npm_version = result.stdout.strip()
        print(f"✅ npm found: {npm_version}")
    except subprocess.CalledProcessError:
        print("❌ npm not found. Please install npm")
        return False
    
    frontend_dir = Path("frontend")
    
    # Create frontend directory if it doesn't exist
    if not frontend_dir.exists():
        print("❌ Frontend directory not found")
        return False
    
    # Install dependencies
    if not run_command("npm install", "Installing dependencies", cwd=frontend_dir):
        return False
    
    # Create additional directories
    directories = [
        "src/components/auth",
        "src/components/dashboard",
        "src/components/chat",
        "src/components/project",
        "src/components/file",
        "src/components/version",
        "src/hooks",
        "src/store",
        "src/utils",
        "public/icons"
    ]
    
    for directory in directories:
        dir_path = frontend_dir / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"📁 Created directory: {directory}")
    
    print("\n🎉 Frontend setup completed successfully!")
    print("\nNext steps:")
    print("1. Start the development server: cd frontend && npm run dev")
    print("2. The frontend will be available at: http://localhost:3000")
    print("3. Make sure the backend is running at: http://localhost:8000")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
