"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: function() { return /* binding */ apiClient; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\nconst API_URL = \"http://localhost:8000\" || 0;\nclass ApiClient {\n    getToken() {\n        if (true) {\n            return localStorage.getItem(\"auth_token\");\n        }\n        return null;\n    }\n    setToken(token) {\n        if (true) {\n            localStorage.setItem(\"auth_token\", token);\n        }\n    }\n    removeToken() {\n        if (true) {\n            localStorage.removeItem(\"auth_token\");\n        }\n    }\n    // Auth methods\n    async register(userData) {\n        const response = await this.client.post(\"/auth/register\", userData);\n        console.log(\"Register response:\", response.data) // Debug log\n        ;\n        if (response.data.access_token) {\n            this.setToken(response.data.access_token);\n        }\n        return response.data;\n    }\n    async login(credentials) {\n        const response = await this.client.post(\"/auth/login\", credentials);\n        console.log(\"Login response:\", response.data) // Debug log\n        ;\n        if (response.data.access_token) {\n            this.setToken(response.data.access_token);\n        }\n        return response.data;\n    }\n    async getCurrentUser() {\n        const response = await this.client.get(\"/auth/me\");\n        return response.data;\n    }\n    logout() {\n        this.removeToken();\n    }\n    // Project methods\n    async getProjects() {\n        const response = await this.client.get(\"/projects/\");\n        return response.data;\n    }\n    async createProject(projectData) {\n        const response = await this.client.post(\"/projects/\", projectData);\n        return response.data;\n    }\n    async getProject(projectId) {\n        const response = await this.client.get(\"/projects/\".concat(projectId));\n        return response.data;\n    }\n    async updateProject(projectId, projectData) {\n        const response = await this.client.put(\"/projects/\".concat(projectId), projectData);\n        return response.data;\n    }\n    async deleteProject(projectId) {\n        const response = await this.client.delete(\"/projects/\".concat(projectId));\n        return response.data;\n    }\n    // Chat methods\n    async sendMessage(projectId, content) {\n        // This returns a stream, so we handle it differently\n        const response = await fetch(\"\".concat(API_URL, \"/chat/\").concat(projectId, \"/message\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(this.getToken())\n            },\n            body: JSON.stringify({\n                content\n            })\n        });\n        if (!response.ok) {\n            throw new Error(\"HTTP error! status: \".concat(response.status));\n        }\n        return response;\n    }\n    async getChatHistory(projectId) {\n        const response = await this.client.get(\"/chat/\".concat(projectId, \"/history\"));\n        return response.data;\n    }\n    async getVersionPreview(projectId, versionId) {\n        const response = await this.client.get(\"/chat/\".concat(projectId, \"/versions/\").concat(versionId, \"/preview\"));\n        return response.data;\n    }\n    // Sharing methods\n    async getSharedProject(token) {\n        const response = await this.client.get(\"/projects/shared/\".concat(token));\n        return response.data;\n    }\n    async forkProject(token) {\n        const response = await this.client.post(\"/projects/fork/\".concat(token));\n        return response.data;\n    }\n    // Health check\n    async healthCheck() {\n        const response = await this.client.get(\"/health\");\n        return response.data;\n    }\n    constructor(){\n        this.client = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n            baseURL: API_URL,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        // Request interceptor to add auth token\n        this.client.interceptors.request.use((config)=>{\n            const token = this.getToken();\n            if (token) {\n                config.headers.Authorization = \"Bearer \".concat(token);\n            }\n            return config;\n        }, (error)=>{\n            return Promise.reject(error);\n        });\n        // Response interceptor to handle auth errors\n        this.client.interceptors.response.use((response)=>response, (error)=>{\n            var _error_response;\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                this.removeToken();\n                window.location.href = \"/auth/login\";\n            }\n            return Promise.reject(error);\n        });\n    }\n}\nconst apiClient = new ApiClient();\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiClient);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});