@tailwind base;
@tailwind components;
@tailwind utilities;

/* Prevent flash of incorrect theme */
html {
  color-scheme: dark;
}

html.light {
  color-scheme: light;
}

@layer base {
  :root {
    /* Base variables - shared between themes */
    --radius: 0.5rem;
  }

  /* Light theme variables */
  .light {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
  }

  /* Dark theme variables */
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  @apply bg-muted;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

/* Code editor styles */
.monaco-editor {
  @apply rounded-md;
}

/* Chat message animations */
.message-enter {
  animation: slide-in 0.3s ease-out;
}

.typing-indicator {
  animation: pulse 1.5s ease-in-out infinite;
}

/* File tree styles */
.file-tree-item {
  @apply hover:bg-accent/50 transition-colors duration-150;
}

.file-tree-item.selected {
  @apply bg-accent text-accent-foreground;
}

/* Version tree styles */
.version-node {
  @apply relative;
}

.version-line {
  @apply absolute left-4 top-8 w-px bg-border h-full;
}

.version-line.last {
  @apply h-4;
}

/* Loading states */
.loading-skeleton {
  @apply animate-pulse bg-muted rounded;
}

/* Toast animations */
.toast-enter {
  animation: slide-in 0.2s ease-out;
}

.toast-exit {
  animation: fade-out 0.2s ease-in;
}

@keyframes fade-out {
  from { opacity: 1; }
  to { opacity: 0; }
}

/* Responsive utilities */
@media (max-width: 768px) {
  .mobile-hidden {
    @apply hidden;
  }

  .mobile-full {
    @apply w-full;
  }
}
