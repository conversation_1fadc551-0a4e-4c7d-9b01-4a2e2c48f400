"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/theme-toggle.tsx":
/*!*****************************************!*\
  !*** ./src/components/theme-toggle.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeToggle: function() { return /* binding */ ThemeToggle; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(app-pages-browser)/./src/components/theme-provider.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ ThemeToggle auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ThemeToggle() {\n    _s();\n    const { theme, setTheme } = (0,_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        setMounted(true);\n    }, []);\n    const toggleTheme = ()=>{\n        setTheme(theme === \"light\" ? \"dark\" : \"light\");\n    };\n    // Always render the button structure to avoid layout shift\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n        variant: \"ghost\",\n        size: \"icon\",\n        onClick: mounted ? toggleTheme : undefined,\n        className: \"h-9 w-9 relative\",\n        title: mounted ? \"Switch to \".concat(theme === \"light\" ? \"dark\" : \"light\", \" mode\") : \"Toggle theme\",\n        disabled: !mounted,\n        children: [\n            mounted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\theme-toggle.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\theme-toggle.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-4 w-4 bg-muted-foreground/20 rounded animate-pulse\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\theme-toggle.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"sr-only\",\n                children: \"Toggle theme\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\theme-toggle.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\theme-toggle.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n_s(ThemeToggle, \"uGU5l7ciDSfqFDe6wS7vfMb29jQ=\", false, function() {\n    return [\n        _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\n_c = ThemeToggle;\nvar _c;\n$RefreshReg$(_c, \"ThemeToggle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/theme-toggle.tsx\n"));

/***/ })

});