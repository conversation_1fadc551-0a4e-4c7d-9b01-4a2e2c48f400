'use client'

export function ThemeScript() {
  const themeScript = `
    (function() {
      try {
        // Apply theme
        var theme = localStorage.getItem('codora-ui-theme') || 'dark';
        var root = document.documentElement;

        // Remove any existing theme classes
        root.classList.remove('light', 'dark');

        // Apply the stored theme immediately before any rendering
        if (theme === 'system') {
          var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
          root.classList.add(systemTheme);
          root.style.colorScheme = systemTheme;
        } else {
          root.classList.add(theme);
          root.style.colorScheme = theme;
        }

        // Check auth state and set global flag
        var authStorage = localStorage.getItem('auth-storage');
        var isAuthenticated = false;

        if (authStorage) {
          try {
            var authData = JSON.parse(authStorage);
            isAuthenticated = !!(authData.state && authData.state.token && authData.state.isAuthenticated);
          } catch (e) {
            // Invalid auth data
            isAuthenticated = false;
          }
        }

        // Set global flags for immediate access
        window.__INITIAL_AUTH_STATE__ = isAuthenticated;
        window.__THEME_READY__ = true;

        // Set attributes for CSS targeting
        root.setAttribute('data-theme-ready', 'true');
        root.setAttribute('data-auth-state', isAuthenticated ? 'authenticated' : 'unauthenticated');

      } catch (e) {
        // Fallback
        document.documentElement.classList.add('dark');
        document.documentElement.style.colorScheme = 'dark';
        document.documentElement.setAttribute('data-theme-ready', 'true');
        document.documentElement.setAttribute('data-auth-state', 'unauthenticated');
        window.__INITIAL_AUTH_STATE__ = false;
        window.__THEME_READY__ = true;
      }
    })();
  `;

  return (
    <script
      dangerouslySetInnerHTML={{
        __html: themeScript,
      }}
    />
  );
}
