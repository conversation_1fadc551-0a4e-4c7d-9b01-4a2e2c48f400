'use client'

export function ThemeScript() {
  const themeScript = `
    (function() {
      try {
        var theme = localStorage.getItem('codora-ui-theme') || 'dark';
        var root = document.documentElement;

        // Remove any existing theme classes
        root.classList.remove('light', 'dark');

        // Apply the stored theme immediately before any rendering
        if (theme === 'system') {
          var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
          root.classList.add(systemTheme);
          root.style.colorScheme = systemTheme;
        } else {
          root.classList.add(theme);
          root.style.colorScheme = theme;
        }

        // Set a flag to indicate theme has been applied
        root.setAttribute('data-theme-ready', 'true');
      } catch (e) {
        // Fallback to dark theme if there's an error
        document.documentElement.classList.add('dark');
        document.documentElement.style.colorScheme = 'dark';
        document.documentElement.setAttribute('data-theme-ready', 'true');
      }
    })();
  `;

  return (
    <script
      dangerouslySetInnerHTML={{
        __html: themeScript,
      }}
    />
  );
}
