'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { cn } from '@/lib/utils'
import {
  Home,
  FolderOpen,
  MessageSquare,
  GitBranch,
  Share2,
  Star,
  Clock,
  Settings,
  ChevronLeft,
  ChevronRight,
  Plus,
} from 'lucide-react'

const sidebarItems = [
  {
    title: 'Overview',
    href: '/dashboard',
    icon: Home,
  },
  {
    title: 'Projects',
    href: '/dashboard/projects',
    icon: FolderOpen,
  },
  {
    title: 'Recent Chats',
    href: '/dashboard/chats',
    icon: MessageSquare,
  },
  {
    title: 'Versions',
    href: '/dashboard/versions',
    icon: GitBranch,
  },
  {
    title: 'Shared',
    href: '/dashboard/shared',
    icon: Share2,
  },
  {
    title: 'Favorites',
    href: '/dashboard/favorites',
    icon: Star,
  },
  {
    title: 'History',
    href: '/dashboard/history',
    icon: Clock,
  },
]

export function DashboardSidebar() {
  const pathname = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
      if (window.innerWidth < 768) {
        setIsCollapsed(true)
      }
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  return (
    <div
      className={cn(
        'relative flex flex-col border-r border-border bg-background transition-all duration-300',
        isCollapsed ? 'w-16' : 'w-64'
      )}
    >
      {/* Collapse Toggle */}
      <Button
        variant="ghost"
        size="icon"
        onClick={() => setIsCollapsed(!isCollapsed)}
        className="absolute -right-3 top-6 z-10 h-6 w-6 rounded-full border border-border bg-background shadow-md"
      >
        {isCollapsed ? (
          <ChevronRight className="h-3 w-3" />
        ) : (
          <ChevronLeft className="h-3 w-3" />
        )}
      </Button>

      {/* Sidebar Content */}
      <div className="flex flex-col h-full">
        {/* Quick Actions */}
        <div className="p-4 border-b border-border">
          <Button
            asChild
            className={cn(
              'w-full justify-start',
              isCollapsed && 'px-2'
            )}
          >
            <Link href="/dashboard/projects/new">
              <Plus className="h-4 w-4" />
              {!isCollapsed && <span className="ml-2">New Project</span>}
            </Link>
          </Button>
        </div>

        {/* Navigation */}
        <ScrollArea className="flex-1 px-3 py-4">
          <nav className="space-y-2">
            {sidebarItems.map((item) => {
              const isActive = pathname === item.href || 
                (item.href !== '/dashboard' && pathname.startsWith(item.href))

              return (
                <Button
                  key={item.href}
                  variant={isActive ? 'secondary' : 'ghost'}
                  className={cn(
                    'w-full justify-start',
                    isCollapsed && 'px-2',
                    isActive && 'bg-accent text-accent-foreground'
                  )}
                  asChild
                >
                  <Link href={item.href}>
                    <item.icon className="h-4 w-4" />
                    {!isCollapsed && (
                      <span className="ml-2">{item.title}</span>
                    )}
                  </Link>
                </Button>
              )
            })}
          </nav>
        </ScrollArea>

        {/* Recent Projects */}
        {!isCollapsed && (
          <div className="border-t border-border p-4">
            <h3 className="text-sm font-medium text-muted-foreground mb-3">
              Recent Projects
            </h3>
            <div className="space-y-2">
              <Button
                variant="ghost"
                className="w-full justify-start text-sm h-8"
                asChild
              >
                <Link href="/dashboard/projects/1">
                  <FolderOpen className="h-3 w-3 mr-2" />
                  My Portfolio
                </Link>
              </Button>
              <Button
                variant="ghost"
                className="w-full justify-start text-sm h-8"
                asChild
              >
                <Link href="/dashboard/projects/2">
                  <FolderOpen className="h-3 w-3 mr-2" />
                  E-commerce Site
                </Link>
              </Button>
              <Button
                variant="ghost"
                className="w-full justify-start text-sm h-8"
                asChild
              >
                <Link href="/dashboard/projects/3">
                  <FolderOpen className="h-3 w-3 mr-2" />
                  Landing Page
                </Link>
              </Button>
            </div>
          </div>
        )}

        {/* Settings */}
        <div className="border-t border-border p-4">
          <Button
            variant="ghost"
            className={cn(
              'w-full justify-start',
              isCollapsed && 'px-2'
            )}
            asChild
          >
            <Link href="/dashboard/settings">
              <Settings className="h-4 w-4" />
              {!isCollapsed && <span className="ml-2">Settings</span>}
            </Link>
          </Button>
        </div>
      </div>
    </div>
  )
}
