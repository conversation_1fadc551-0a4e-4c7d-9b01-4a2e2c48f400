"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_theme_toggle__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/theme-toggle */ \"(app-pages-browser)/./src/components/theme-toggle.tsx\");\n/* harmony import */ var _components_client_only__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/client-only */ \"(app-pages-browser)/./src/components/client-only.tsx\");\n/* harmony import */ var _components_auth_buttons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/auth-buttons */ \"(app-pages-browser)/./src/components/auth-buttons.tsx\");\n/* harmony import */ var _hooks_use_auth__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-auth */ \"(app-pages-browser)/./src/hooks/use-auth.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Code2,GitBranch,Globe,MessageSquare,Rocket,Sparkles,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Code2,GitBranch,Globe,MessageSquare,Rocket,Sparkles,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Code2,GitBranch,Globe,MessageSquare,Rocket,Sparkles,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/git-branch.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Code2,GitBranch,Globe,MessageSquare,Rocket,Sparkles,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Code2,GitBranch,Globe,MessageSquare,Rocket,Sparkles,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Code2,GitBranch,Globe,MessageSquare,Rocket,Sparkles,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Code2,GitBranch,Globe,MessageSquare,Rocket,Sparkles,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Code2,GitBranch,Globe,MessageSquare,Rocket,Sparkles,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Code2,GitBranch,Globe,MessageSquare,Rocket,Sparkles,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isLoggedIn, isHydrated, getCurrentUser } = (0,_hooks_use_auth__WEBPACK_IMPORTED_MODULE_9__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check authentication status on mount\n        console.log(\"Home page - checking auth status:\", {\n            isLoggedIn,\n            isHydrated\n        }) // Debug log\n        ;\n        if (isHydrated && !isLoggedIn) {\n            getCurrentUser();\n        }\n    }, [\n        getCurrentUser,\n        isLoggedIn,\n        isHydrated\n    ]);\n    const handleGetStarted = ()=>{\n        if (isLoggedIn) {\n            router.push(\"/dashboard\");\n        } else {\n            router.push(\"/auth/register\");\n        }\n    };\n    const features = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            title: \"AI-Powered Development\",\n            description: \"Build websites using natural language. Just describe what you want, and our AI creates it for you.\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            title: \"Real-time Streaming\",\n            description: \"Watch your website come to life in real-time as the AI agent works on your project.\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            title: \"Version Control\",\n            description: \"Every change is tracked. Travel back in time to any version of your project.\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            title: \"Collaboration\",\n            description: \"Share projects with your team. Fork and build upon existing projects.\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            title: \"Instant Preview\",\n            description: \"See your website live instantly. Toggle between code view and website preview.\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            title: \"Conversational Interface\",\n            description: \"Chat with the AI to make changes, add features, or fix issues naturally.\"\n        }\n    ];\n    const technologies = [\n        \"HTML5\",\n        \"CSS3\",\n        \"JavaScript\",\n        \"TypeScript\",\n        \"React\",\n        \"Next.js\",\n        \"Tailwind CSS\",\n        \"Node.js\",\n        \"Python\",\n        \"FastAPI\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container flex h-16 items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-6 w-6 text-primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold text-foreground\",\n                                    children: \"Codora\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"#features\",\n                                    className: \"text-sm font-medium hover:text-primary transition-colors\",\n                                    children: \"Features\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"#how-it-works\",\n                                    className: \"text-sm font-medium hover:text-primary transition-colors\",\n                                    children: \"How it Works\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"#pricing\",\n                                    className: \"text-sm font-medium hover:text-primary transition-colors\",\n                                    children: \"Pricing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_toggle__WEBPACK_IMPORTED_MODULE_6__.ThemeToggle, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_client_only__WEBPACK_IMPORTED_MODULE_7__.ClientOnly, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_buttons__WEBPACK_IMPORTED_MODULE_8__.AuthButtons, {\n                                        onGetStarted: handleGetStarted\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"flex-1 flex items-center justify-center py-20 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container max-w-4xl text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center rounded-full border px-3 py-1 text-sm mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                \"AI-Powered Web Development\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl md:text-6xl font-bold tracking-tight mb-6\",\n                            children: [\n                                \"Build Websites with\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-primary\",\n                                    children: \" AI Assistance\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-muted-foreground mb-8 max-w-2xl mx-auto\",\n                            children: \"Transform your ideas into beautiful, functional websites using natural language. No coding experience required - just describe what you want.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    size: \"lg\",\n                                    onClick: handleGetStarted,\n                                    className: \"text-lg px-8\",\n                                    children: [\n                                        \"Start Building\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"ml-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    size: \"lg\",\n                                    variant: \"outline\",\n                                    onClick: ()=>router.push(\"/demo\"),\n                                    children: \"Watch Demo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap justify-center gap-2 mb-8\",\n                            children: technologies.map((tech)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"inline-flex items-center rounded-full bg-muted px-3 py-1 text-xs font-medium\",\n                                    children: tech\n                                }, tech, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                className: \"py-20 bg-muted/30\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold mb-4 text-foreground\",\n                                    children: \"Everything you need to build amazing websites\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-muted-foreground max-w-2xl mx-auto\",\n                                    children: \"Powerful features that make web development accessible to everyone\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    className: \"border border-border bg-card shadow-lg hover:shadow-xl transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                    className: \"h-10 w-10 text-primary mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    className: \"text-xl text-card-foreground\",\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                                className: \"text-base text-muted-foreground\",\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"how-it-works\",\n                className: \"py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                    children: \"How Codora Works\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-muted-foreground max-w-2xl mx-auto\",\n                                    children: \"From idea to website in three simple steps\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-8 w-8 text-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-4\",\n                                            children: \"1. Describe Your Vision\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"Tell our AI what kind of website you want to build using natural language.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-8 w-8 text-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-4\",\n                                            children: \"2. Watch AI Build\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"Our AI agent creates your website in real-time, showing you every step.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-8 w-8 text-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-4\",\n                                            children: \"3. Launch & Share\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"Preview your website instantly and share it with the world.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-gradient-to-r from-slate-900 via-slate-800 to-slate-900 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 light:from-slate-100 light:via-slate-50 light:to-slate-100 border-y border-border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-4 text-white dark:text-white light:text-slate-900\",\n                            children: \"Ready to build your next website?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl mb-8 text-slate-300 dark:text-slate-300 light:text-slate-600 max-w-2xl mx-auto\",\n                            children: \"Join thousands of creators who are building amazing websites with AI assistance.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            size: \"lg\",\n                            onClick: handleGetStarted,\n                            className: \"text-lg px-8 bg-primary hover:bg-primary/90 text-primary-foreground\",\n                            children: [\n                                \"Get Started for Free\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"ml-2 h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"border-t border-border bg-background py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 mb-4 md:mb-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Code2_GitBranch_Globe_MessageSquare_Rocket_Sparkles_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5 text-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-foreground\",\n                                            children: \"Codora\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6 text-sm text-muted-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/privacy\",\n                                            className: \"hover:text-foreground transition-colors\",\n                                            children: \"Privacy Policy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/terms\",\n                                            className: \"hover:text-foreground transition-colors\",\n                                            children: \"Terms of Service\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/contact\",\n                                            className: \"hover:text-foreground transition-colors\",\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 pt-8 border-t border-border text-center text-sm text-muted-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"\\xa9 2024 Codora. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"mnHDghiJqEAbcYG7A6OxrtQv3Co=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_auth__WEBPACK_IMPORTED_MODULE_9__.useAuth\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});