"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/dashboard/sidebar.tsx":
/*!**********************************************!*\
  !*** ./src/components/dashboard/sidebar.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardSidebar: function() { return /* binding */ DashboardSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,FolderOpen,GitBranch,Home,MessageSquare,Plus,Settings,Share2,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,FolderOpen,GitBranch,Home,MessageSquare,Plus,Settings,Share2,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,FolderOpen,GitBranch,Home,MessageSquare,Plus,Settings,Share2,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,FolderOpen,GitBranch,Home,MessageSquare,Plus,Settings,Share2,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/git-branch.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,FolderOpen,GitBranch,Home,MessageSquare,Plus,Settings,Share2,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,FolderOpen,GitBranch,Home,MessageSquare,Plus,Settings,Share2,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,FolderOpen,GitBranch,Home,MessageSquare,Plus,Settings,Share2,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,FolderOpen,GitBranch,Home,MessageSquare,Plus,Settings,Share2,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,FolderOpen,GitBranch,Home,MessageSquare,Plus,Settings,Share2,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,FolderOpen,GitBranch,Home,MessageSquare,Plus,Settings,Share2,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Clock,FolderOpen,GitBranch,Home,MessageSquare,Plus,Settings,Share2,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ DashboardSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst sidebarItems = [\n    {\n        title: \"Overview\",\n        href: \"/dashboard\",\n        icon: _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        title: \"Projects\",\n        href: \"/dashboard/projects\",\n        icon: _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        title: \"Recent Chats\",\n        href: \"/dashboard/chats\",\n        icon: _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        title: \"Versions\",\n        href: \"/dashboard/versions\",\n        icon: _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        title: \"Shared\",\n        href: \"/dashboard/shared\",\n        icon: _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        title: \"Favorites\",\n        href: \"/dashboard/favorites\",\n        icon: _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        title: \"History\",\n        href: \"/dashboard/history\",\n        icon: _barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    }\n];\nfunction DashboardSidebar() {\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n            if (window.innerWidth < 768) {\n                setIsCollapsed(true);\n            }\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"relative flex flex-col border-r border-border bg-background transition-all duration-300\", isCollapsed ? \"w-16\" : \"w-64\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                variant: \"ghost\",\n                size: \"icon\",\n                onClick: ()=>setIsCollapsed(!isCollapsed),\n                className: \"absolute -right-3 top-6 z-10 h-6 w-6 rounded-full border border-border bg-background shadow-md\",\n                children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-3 w-3\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-3 w-3\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/dashboard/projects/new\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-full justify-start\", isCollapsed && \"px-2\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this),\n                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2\",\n                                        children: \"New Project\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 32\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                        className: \"flex-1 px-3 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"space-y-2\",\n                            children: sidebarItems.map((item)=>{\n                                const isActive = pathname === item.href || item.href !== \"/dashboard\" && pathname.startsWith(item.href);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: isActive ? \"secondary\" : \"ghost\",\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-full justify-start\", isCollapsed && \"px-2\", isActive && \"bg-accent text-accent-foreground\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 21\n                                            }, this),\n                                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2\",\n                                                children: item.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 19\n                                    }, this)\n                                }, item.href, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-border p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-muted-foreground mb-3\",\n                                children: \"Recent Projects\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"w-full justify-start text-sm h-8\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/dashboard/projects/1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"My Portfolio\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"w-full justify-start text-sm h-8\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/dashboard/projects/2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"E-commerce Site\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"w-full justify-start text-sm h-8\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/dashboard/projects/3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Landing Page\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-border p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"ghost\",\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-full justify-start\", isCollapsed && \"px-2\"),\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/dashboard/settings\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Clock_FolderOpen_GitBranch_Home_MessageSquare_Plus_Settings_Share2_Star_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this),\n                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2\",\n                                        children: \"Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 32\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardSidebar, \"QaaEW3BjgaXKiZ3hzJElJ+Hnk6w=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = DashboardSidebar;\nvar _c;\n$RefreshReg$(_c, \"DashboardSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/sidebar.tsx\n"));

/***/ })

});