from agent import Codora
import os
from dotenv import load_dotenv

load_dotenv()

agent = Codora(
    model_name=os.getenv("MODEL_NAME", "gpt-4o-mini"),
    model_provider=os.getenv("MODEL_PROVIDER", "openai")
)


while True:
    user_input = input("💬 You: ").strip()
    
    if not user_input:
        continue
    
    print(f"🤖 Assistant: ", end="")
    
    for x in agent.process_message(user_input, thread_id="main_session"):
        print(x)