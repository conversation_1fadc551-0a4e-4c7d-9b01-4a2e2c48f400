'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/store/auth'
import { Loader2 } from 'lucide-react'

interface ProtectedRouteProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function ProtectedRoute({ children, fallback }: ProtectedRouteProps) {
  const router = useRouter()
  const { isAuthenticated, token, getCurrentUser, isLoading } = useAuthStore()
  const [isChecking, setIsChecking] = useState(true)

  useEffect(() => {
    const checkAuth = async () => {
      if (token && !isAuthenticated) {
        // We have a token but no user data, try to get current user
        try {
          await getCurrentUser()
        } catch (error) {
          // If getting user fails, redirect to login
          router.push('/auth/login')
          return
        }
      } else if (!token) {
        // No token, redirect to login
        router.push('/auth/login')
        return
      }
      
      setIsChecking(false)
    }

    checkAuth()
  }, [token, isAuthenticated, getCurrentUser, router])

  // Show loading while checking authentication
  if (isChecking || isLoading) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center bg-background">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
      )
    )
  }

  // If not authenticated after checking, don't render children
  if (!isAuthenticated) {
    return null
  }

  return <>{children}</>
}

// Hook for checking authentication status
export function useAuth() {
  const { isAuthenticated, user, token, logout } = useAuthStore()
  
  return {
    isAuthenticated,
    user,
    token,
    logout,
  }
}
