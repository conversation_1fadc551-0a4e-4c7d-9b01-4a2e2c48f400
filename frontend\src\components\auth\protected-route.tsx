'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/use-auth'
import { Loader2 } from 'lucide-react'

interface ProtectedRouteProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function ProtectedRoute({ children, fallback }: ProtectedRouteProps) {
  const router = useRouter()
  const { isLoggedIn, isHydrated, isReady, token, getCurrentUser, isLoading } = useAuth()
  const [isChecking, setIsChecking] = useState(true)

  useEffect(() => {
    if (!isHydrated) return // Don't check auth until hydrated

    const checkAuth = async () => {
      console.log('Protected route check:', {
        token: !!token,
        isLoggedIn,
        isHydrated,
        isReady
      }) // Debug log

      if (isLoggedIn) {
        // User is authenticated and hydrated
        console.log('Auth check passed - user authenticated') // Debug log
        setIsChecking(false)
        return
      }

      if (token && !isLoggedIn) {
        // We have a token but user is not authenticated, try to get current user
        console.log('Token found but not authenticated, getting current user') // Debug log
        try {
          await getCurrentUser()
          setIsChecking(false)
        } catch (error) {
          console.error('Failed to get current user:', error) // Debug log
          // If getting user fails, redirect to login
          router.push('/auth/login')
          return
        }
      } else if (!token) {
        // No token, redirect to login
        console.log('No token found, redirecting to login') // Debug log
        router.push('/auth/login')
        return
      }
    }

    checkAuth()
  }, [token, isLoggedIn, isHydrated, isReady, getCurrentUser, router])

  // Show loading while checking authentication
  if (isChecking || isLoading) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center bg-background">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
      )
    )
  }

  // If not authenticated after checking, don't render children
  if (!isLoggedIn) {
    return null
  }

  return <>{children}</>
}
