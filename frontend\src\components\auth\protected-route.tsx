'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/store/auth'
import { Loader2 } from 'lucide-react'

interface ProtectedRouteProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function ProtectedRoute({ children, fallback }: ProtectedRouteProps) {
  const router = useRouter()
  const { isAuthenticated, token, getCurrentUser, isLoading } = useAuthStore()
  const [isChecking, setIsChecking] = useState(true)

  useEffect(() => {
    const checkAuth = async () => {
      console.log('Protected route check:', { token: !!token, isAuthenticated }) // Debug log

      if (token && isAuthenticated) {
        // We have token and user data, all good
        setIsChecking(false)
        return
      }

      if (token && !isAuthenticated) {
        // We have a token but no user data, try to get current user
        try {
          await getCurrentUser()
          setIsChecking(false)
        } catch (error) {
          console.error('Failed to get current user:', error) // Debug log
          // If getting user fails, redirect to login
          router.push('/auth/login')
          return
        }
      } else if (!token) {
        // No token, redirect to login
        console.log('No token found, redirecting to login') // Debug log
        router.push('/auth/login')
        return
      }
    }

    checkAuth()
  }, [token, isAuthenticated, getCurrentUser, router])

  // Show loading while checking authentication
  if (isChecking || isLoading) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center bg-background">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
      )
    )
  }

  // If not authenticated after checking, don't render children
  if (!isAuthenticated) {
    return null
  }

  return <>{children}</>
}

// Hook for checking authentication status
export function useAuth() {
  const { isAuthenticated, user, token, logout } = useAuthStore()

  return {
    isAuthenticated,
    user,
    token,
    logout,
  }
}
